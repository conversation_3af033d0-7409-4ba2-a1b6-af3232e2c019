#!/bin/bash

# Enhanced script to iterate through vLLM containers and test performance across multiple length categories
# Usage: ./iterate-models-enhanced.sh

set -e

# Configuration
SSH_KEY="id_rsa"
SSH_PORT="10026"
SSH_USER="tdx"
SSH_HOST="localhost"
QEMU_SCRIPT="./start-qemu.sh"
QEMU_PARAMS="-i ../ima-w-log.qcow2 -t efi -b direct -k vmlinuz"
RESULTS_DIR="./NON_TDX-0-model_results"
TIMEOUT_VM_START=600  # 10 minutes timeout for VM to start
TIMEOUT_SERVICE_READY=1800 # 30 minutes timeout for service to be ready
PYTHON_SCRIPT="qps_performance_test.py"
JSON_FILE="sampled_100_prompts_with_distribution.json"
HF_ENDPOINT="https://hf-mirror.com"  # Hugging Face mirror endpoint

# QPS rates to test
QPS_RATES=(2 4 8 16 32)

# Container to model mapping (matching QPS script models)
declare -A CONTAINER_TO_MODEL=(
    ["vllm-deepseek-r1-llama-8b"]="deepseek-ai/DeepSeek-R1-Distill-Llama-8B"
    ["vllm-phi-4-reasoning"]="microsoft/Phi-4-reasoning"
    ["vllm-deepseek-r1-qwen-14b"]="deepseek-ai/DeepSeek-R1-Distill-Qwen-14B"
    ["vllm-qwen3-8b"]="Qwen/Qwen3-8B"
    ["vllm-qwen3-0-6b"]="Qwen/Qwen3-0.6B"
    ["vllm-llama31-8b"]="meta-llama/Llama-3.1-8B-Instruct"
)

# Model to family mapping (updated with all models)
declare -A MODEL_TO_FAMILY=(
    ["DeepSeek-R1-Qwen3-8B"]="DeepSeek"
    ["DeepSeek-R1-Distill-Qwen-14B"]="DeepSeek"
    ["DeepSeek-R1-Distill-Llama-8B"]="DeepSeek"
    ["DeepSeek-R1-Distill-Qwen-7B"]="DeepSeek"
    ["DeepSeek-R1-Distill-Qwen-1.5B"]="DeepSeek"
    ["Qwen3-14B"]="Qwen3"
    ["Qwen3-8B"]="Qwen3"
    ["Qwen3-4B"]="Qwen3"
    ["Qwen3-1.7B"]="Qwen3"
    ["Qwen3-0.6B"]="Qwen3"
    ["Llama-3.2-1B-Instruct"]="Llama"
    ["Llama-3.2-3B-Instruct"]="Llama"
    ["Llama-3.1-8B"]="Llama"
    ["Llama-3.2-11B-Vision"]="Llama"
    ["Phi-4-mini-flash-reasoning"]="Phi"
    ["Phi-4-mini-reasoning"]="Phi"
    ["Phi-4-reasoning"]="Phi"
    ["Phi-4-reasoning-plus"]="Phi"
    ["Phi-4-multimodal-instruct"]="Phi"
    ["Mistral-7B-Instruct-v0.2"]="Mistral"
    ["Mixtral-8x7B-Instruct-v0.1"]="Mistral"
    ["Mistral-Nemo-Instruct-2407"]="Mistral"
    ["Mixtral-8x22B-v0.1"]="Mistral"
    ["Ministral-8B-Instruct-2410"]="Mistral"
)

# All available vLLM containers to test (based on your Docker setup)
CONTAINERS=(
    "vllm-deepseek-r1-qwen-14b"
)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

# Function to wait for VM to be accessible via SSH
wait_for_vm() {
    local container_name="$1"
    local count=0
    local max_attempts=$((TIMEOUT_VM_START / 10))
    
    log "Waiting for VM to be accessible via SSH..."
    
    while [ $count -lt $max_attempts ]; do
        if ssh -p $SSH_PORT -i $SSH_KEY -o ConnectTimeout=5 -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $SSH_USER@$SSH_HOST "echo 'VM Ready'" &>/dev/null; then
            log_success "VM is accessible via SSH"
            return 0
        fi
        count=$((count + 1))
        echo -n "."
        sleep 10
    done
    
    log_error "VM failed to become accessible within $TIMEOUT_VM_START seconds"
    return 1
}

# Function to wait for service to be ready (VM CSV indicates service is up)
wait_for_service_ready() {
    local container_name="$1"
    local count=0
    local max_attempts=$((TIMEOUT_SERVICE_READY / 30))
    
    log "Waiting for vLLM service to be ready (VM CSV detection)..."
    
    while [ $count -lt $max_attempts ]; do
        # Check if VM-generated CSV file exists (service ready indicator)
        if ssh -p $SSH_PORT -i $SSH_KEY -o ConnectTimeout=10 -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $SSH_USER@$SSH_HOST "ls result-${container_name}-*.csv" &>/dev/null; then
            log_success "Service is ready - VM CSV detected"
            return 0
        fi
        
        # Check if the service failed
        if ssh -p $SSH_PORT -i $SSH_KEY -o ConnectTimeout=10 -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $SSH_USER@$SSH_HOST "systemctl is-failed vllm-with-tracking.service" &>/dev/null; then
            log_error "vLLM tracking service failed"
            return 1
        fi
        
        count=$((count + 1))
        echo -n "."
        sleep 30
    done
    
    log_error "Service did not become ready within $TIMEOUT_SERVICE_READY seconds"
    return 1
}

# Function to check local prerequisites
check_local_prerequisites() {
    log "Checking local prerequisites..."
    
    # Check if script exists locally
    if [[ ! -f "$PYTHON_SCRIPT" ]]; then
        log_error "Python script not found: $PYTHON_SCRIPT"
        return 1
    fi
    
    if [[ ! -f "$JSON_FILE" ]]; then
        log_error "JSON file not found: $JSON_FILE"
        return 1
    fi
    
    # Check if Python script works with HF_ENDPOINT
    if ! HF_ENDPOINT="$HF_ENDPOINT" python3 "$PYTHON_SCRIPT" --list &>/dev/null; then
        log_error "Python script test failed - check dependencies and HF_ENDPOINT"
        return 1
    fi
    
    log_success "Local prerequisites check passed"
    return 0
}

# Function to test a single QPS rate (Python script execution)
test_qps_rate() {
    local model_name="$1"
    local qps_rate="$2"
    local result_file="$3"

    log "Running Python QPS test for rate $qps_rate..."

    # Run the QPS Python script on HOST with HF_ENDPOINT (not in VM)
    # The Python script will connect to the vLLM service running in the VM
    if HF_ENDPOINT="$HF_ENDPOINT" python3 "$PYTHON_SCRIPT" --models "$model_name" --qps "$qps_rate" --prewarm --prompts-limit 100; then
        log_success "Python QPS test for rate $qps_rate completed"
        return 0
    else
        log_error "Python QPS test for rate $qps_rate failed"
        return 1
    fi
}

# Function to collect VM CSV for a specific test
collect_vm_csv() {
    local container_name="$1"
    local model_name="$2"
    local qps_rate="$3"

    log "Collecting VM CSV for $model_name QPS $qps_rate..."

    # Create temporary directory for this test's VM CSV
    local temp_dir="./temp_vm_csv"
    mkdir -p "$temp_dir"

    # Collect VM-generated CSV (service readiness indicator)
    if ssh -p $SSH_PORT -i $SSH_KEY -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $SSH_USER@$SSH_HOST "ls result-${container_name}-*.csv" &>/dev/null; then
        local vm_csv_target="${model_name//\//_}_qps_${qps_rate}_vm_service_$(date +%Y%m%d_%H%M%S).csv"
        scp -P $SSH_PORT -i $SSH_KEY -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null "$SSH_USER@$SSH_HOST:result-${container_name}-*.csv" "$temp_dir/$vm_csv_target"
        log_success "Collected VM service CSV -> $vm_csv_target"

        # Clean up VM CSV
        ssh -p $SSH_PORT -i $SSH_KEY -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $SSH_USER@$SSH_HOST "rm -f result-${container_name}-*.csv" &>/dev/null || true
    else
        log_warn "No VM service CSV found for QPS $qps_rate"
    fi
}

# Function to test a single QPS rate with VM boot
test_single_qps_with_vm_boot() {
    local container_name="$1"
    local model_name="$2"
    local qps_rate="$3"

    log "Booting VM for container $container_name to test QPS rate $qps_rate..."

    # Start QEMU VM with the container name
    log "Starting QEMU VM with: $QEMU_SCRIPT $QEMU_PARAMS -x $container_name"
    $QEMU_SCRIPT $QEMU_PARAMS -x "$container_name" &
    local qemu_pid=$!

    # Wait for VM to start
    if wait_for_vm "$container_name"; then
        # Wait for service to be ready (VM generates CSV when service is ready)
        if wait_for_service_ready "$container_name"; then
            # Test this specific QPS rate
            local result_file="qps_${qps_rate}_results.csv"
            if test_qps_rate "$model_name" "$qps_rate" "$result_file"; then
                log_success "QPS rate $qps_rate test completed successfully"
                local test_result=0
            else
                log_error "QPS rate $qps_rate test failed"
                local test_result=1
            fi

            # Collect VM CSV for this test
            collect_vm_csv "$container_name" "$model_name" "$qps_rate"
        else
            log_error "Service failed to become ready for $container_name (QPS $qps_rate)"
            local test_result=1
        fi
    else
        log_error "VM failed to start for $container_name (QPS $qps_rate)"
        local test_result=1
    fi

    # Shutdown VM
    shutdown_vm "$container_name"

    # Make sure QEMU process is dead
    kill $qemu_pid 2>/dev/null || true
    wait $qemu_pid 2>/dev/null || true

    return $test_result
}

# Function to collect and organize results (called after all lengths tested)
collect_and_organize_results() {
    local container_name="$1"
    local model_name="$2"
    local model_family="$3"
    
    log "Organizing results for $model_name..."
    
    # Create family directory
    local family_dir="$RESULTS_DIR/$model_family"
    mkdir -p "$family_dir"
    
    local collected_count=0
    
    # Move host-generated Python CSV files (performance metrics)
    log "Organizing host-generated performance metrics..."
    for qps in "${QPS_RATES[@]}"; do
        local result_file="performance_detailed_${model_name//\//_}_${qps}qps_*.csv"
        local target_file="${model_name//\//_}_qps_${qps}_$(date +%Y%m%d_%H%M%S).csv"

        # Check if result file exists locally (generated by host Python script)
        if ls $result_file 1> /dev/null 2>&1; then
            # Move the most recent file to family directory
            local latest_file=$(ls -t $result_file | head -n1)
            mv "$latest_file" "$family_dir/$target_file"
            log_success "Organized performance metrics for QPS $qps -> $target_file"
            collected_count=$((collected_count + 1))
        else
            log_warn "No performance metrics found for length category $length"
        fi
    done
    
    # Move VM CSV files from temp directory
    log "Organizing VM service CSV files..."
    local temp_dir="./temp_vm_csv"
    if [[ -d "$temp_dir" ]]; then
        find "$temp_dir" -name "${model_name}_length_*_vm_service_*.csv" -type f | while read vm_csv_file; do
            if [[ -f "$vm_csv_file" ]]; then
                mv "$vm_csv_file" "$family_dir/"
                log_success "Organized VM CSV -> $(basename "$vm_csv_file")"
            fi
        done
        
        # Clean up temp directory
        rm -rf "$temp_dir"
    fi
    
    log_success "Organized $collected_count performance metric files for $model_name in $family_dir"
    return 0
}

# Function to test all length categories for a model (one VM boot per length)
test_all_lengths() {
    local container_name="$1"
    local model_name="$2"
    local model_family="$3"
    
    log "Testing all QPS rates for $model_name (one VM boot per QPS)..."

    local successful_tests=0
    local total_tests=${#QPS_RATES[@]}

    # Test each QPS rate with separate VM boot
    for qps in "${QPS_RATES[@]}"; do
        log "=== Testing QPS rate $qps for $model_name ==="

        # Boot VM for this specific QPS test
        if test_single_qps_with_vm_boot "$container_name" "$model_name" "$qps"; then
            successful_tests=$((successful_tests + 1))
            log_success "QPS rate $qps completed successfully"
        else
            log_error "QPS rate $qps failed"
        fi
        
        # Delay between VM boots
        log "Waiting 30 seconds before next VM boot..."
        sleep 30
    done
    
    log "Completed $successful_tests/$total_tests length category tests for $model_name"
    
    # Collect and organize all results at the end
    collect_and_organize_results "$container_name" "$model_name" "$model_family"
    
    return 0
}

# Function to shutdown VM
shutdown_vm() {
    local container_name="$1"
    
    log "Shutting down VM for $container_name..."
    
    # Try graceful shutdown first
    ssh -p $SSH_PORT -i $SSH_KEY -o ConnectTimeout=10 -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $SSH_USER@$SSH_HOST "sudo shutdown -h now" &>/dev/null || true
    
    # Wait a bit for graceful shutdown
    sleep 10
    
    # Force kill any remaining QEMU processes
    pkill -f "tdxvm" || true
    pkill -f "qemu-system-x86_64.*tdxvm" || true
    
    log_success "VM shutdown complete"
}

# Function to test a single container with all length categories (one VM boot per length)
test_container_enhanced() {
    local container_name="$1"
    
    # Get model name and family
    local model_name="${CONTAINER_TO_MODEL[$container_name]}"
    local model_family="${MODEL_TO_FAMILY[$model_name]}"
    
    if [[ -z "$model_name" ]]; then
        log_error "Unknown container: $container_name"
        return 1
    fi
    
    echo ""
    echo "========================================"
    log "Starting enhanced test for container: $container_name"
    log "Model: $model_name (Family: $model_family)"
    log "Will test length categories: ${LENGTH_CATEGORIES[*]}"
    log "Architecture: Each length category = separate VM boot"
    echo "========================================"
    
    # Test all length categories (one VM boot per length)
    if test_all_lengths "$container_name" "$model_name" "$model_family"; then
        log_success "All tests completed successfully for $container_name"
    else
        log_error "Some tests failed for $container_name"
    fi
    
    log "Enhanced test completed for $container_name"
    echo ""
}

# Function to check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    if [[ ! -f "$QEMU_SCRIPT" ]]; then
        log_error "QEMU script not found: $QEMU_SCRIPT"
        return 1
    fi
    
    if [[ ! -f "$SSH_KEY" ]]; then
        log_error "SSH key not found: $SSH_KEY"
        return 1
    fi
    
    # Check local Python script and dependencies
    if ! check_local_prerequisites; then
        return 1
    fi
    
    log_success "All prerequisites check passed"
    return 0
}

# Function to get available length categories from JSON
get_available_lengths() {
    if [[ -f "$JSON_FILE" ]]; then
        local available_lengths=$(python3 -c "
import json
with open('$JSON_FILE', 'r') as f:
    data = json.load(f)
print(' '.join(sorted([str(k) for k in data.keys()], key=int)))
" 2>/dev/null)
        
        if [[ -n "$available_lengths" ]]; then
            echo "$available_lengths"
            return 0
        fi
    fi
    
    # Fallback to default if can't read JSON
    echo "2 4 8 16 32 64 128 256 512 1024 2048"
    return 0
}

# Main execution
main() {
    log "Starting enhanced vLLM container iteration script"
    
    # # Check prerequisites
    # if ! check_prerequisites; then
    #     exit 1
    # fi
    
    # Get available length categories from JSON
    if [[ ${#LENGTH_CATEGORIES[@]} -eq 0 ]]; then
        local available_lengths=$(get_available_lengths)
        read -ra LENGTH_CATEGORIES <<< "$available_lengths"
    fi
    
    log "Available length categories: ${LENGTH_CATEGORIES[*]}"
    
    echo "Will test these containers with length categories:"
    for container in "${CONTAINERS[@]}"; do
        local model_name="${CONTAINER_TO_MODEL[$container]}"
        local model_family="${MODEL_TO_FAMILY[$model_name]}"
        echo "  - $container -> $model_name (Family: $model_family)"
    done
    echo ""
    echo "Length categories to test: ${LENGTH_CATEGORIES[*]}"
    echo ""
    
    # Confirm before starting
    read -p "Do you want to test all these containers with all length categories? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log "Operation cancelled by user"
        exit 0
    fi
    
    # Create summary file
    local summary_file="$RESULTS_DIR/enhanced_test_summary_$(date +%Y%m%d_%H%M%S).txt"
    mkdir -p "$RESULTS_DIR"
    echo "Enhanced vLLM Container Performance Test Summary" > "$summary_file"
    echo "Started: $(date)" >> "$summary_file"
    echo "Length categories: ${LENGTH_CATEGORIES[*]}" >> "$summary_file"
    echo "=======================================" >> "$summary_file"
    echo "" >> "$summary_file"
    
    # Test each container
    local total_containers=${#CONTAINERS[@]}
    local current=0
    local successful=0
    local failed=0
    
    for container in "${CONTAINERS[@]}"; do
        current=$((current + 1))
        local model_name="${CONTAINER_TO_MODEL[$container]}"
        log "Testing container $current of $total_containers: $container ($model_name)"
        
        if test_container_enhanced "$container"; then
            successful=$((successful + 1))
            echo "✓ $container ($model_name) - SUCCESS" >> "$summary_file"
        else
            failed=$((failed + 1))
            echo "✗ $container ($model_name) - FAILED" >> "$summary_file"
        fi
        
        # Add delay between tests to ensure clean shutdown
        if [[ $current -lt $total_containers ]]; then
            log "Waiting 60 seconds before next test..."
            sleep 60
        fi
    done
    
    # Final summary
    echo "" >> "$summary_file"
    echo "=======================================" >> "$summary_file"
    echo "Completed: $(date)" >> "$summary_file"
    echo "Total containers: $total_containers" >> "$summary_file"
    echo "Successful: $successful" >> "$summary_file"
    echo "Failed: $failed" >> "$summary_file"
    echo "Length categories tested: ${LENGTH_CATEGORIES[*]}" >> "$summary_file"
    
    echo ""
    echo "========================================"
    log_success "All enhanced tests completed!"
    echo "Total containers: $total_containers"
    echo "Successful: $successful"
    echo "Failed: $failed"
    echo "Length categories tested: ${LENGTH_CATEGORIES[*]}"
    echo "Results directory: $RESULTS_DIR"
    echo "Summary file: $summary_file"
    echo ""
    echo "Results are organized by model family:"
    for family in $(printf '%s\n' "${MODEL_TO_FAMILY[@]}" | sort -u); do
        echo "  - $RESULTS_DIR/$family/"
    done
    echo "========================================"
}

# Handle Ctrl+C
trap 'log_warn "Script interrupted by user"; shutdown_vm "current"; exit 1' INT

# Run main function
main "$@"