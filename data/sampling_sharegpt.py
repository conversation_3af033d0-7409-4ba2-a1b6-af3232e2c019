#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to select ShareGPT prompts based on token length and save to JSON.
Selects 1 prompt for each length: 128, 256, 512, 1024, 2048, 4096, 8192, 16384 tokens using multiple tokenizers.
"""

import json
import random
import requests
import os
from typing import Dict, List, Any
from transformers import AutoTokenizer
from huggingface_hub import login

def setup_huggingface_auth():
    """Setup Hugging Face authentication."""
    # Set the token as environment variable
    hf_token = "*************************************"
    os.environ["HUGGINGFACE_HUB_TOKEN"] = hf_token
    os.environ["HF_TOKEN"] = hf_token  # Also set this for compatibility
    
    try:
        # Login to Hugging Face
        login(token=hf_token, add_to_git_credential=False)
        print("✅ Successfully authenticated with Hugging Face")
        return True
    except Exception as e:
        print(f"❌ Failed to authenticate with Hugging Face: {e}")
        print("Continuing without authentication (some models may not load)")
        return False

def load_tokenizers():
    """Load all required tokenizers and check their max lengths."""
    tokenizer_names = [
        "deepseek-ai/DeepSeek-R1-Distill-Llama-8B",
        "microsoft/Phi-4-reasoning", 
        "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B",
        "Qwen/Qwen3-8B",
        "Qwen/Qwen3-0.6B",
        "meta-llama/Llama-3.1-8B-Instruct"
    ]
    
    tokenizers = {}
    tokenizer_info = {}
    
    for name in tokenizer_names:
        try:
            print(f"Loading tokenizer: {name}")
            
            # Try different authentication approaches
            tokenizer = None
            
            # First try with token=True
            try:
                tokenizer = AutoTokenizer.from_pretrained(name, token=True)
            except Exception as e1:
                # If that fails, try with explicit token
                try:
                    tokenizer = AutoTokenizer.from_pretrained(name, token="*************************************")
                except Exception as e2:
                    # If that fails, try without authentication
                    try:
                        tokenizer = AutoTokenizer.from_pretrained(name)
                    except Exception as e3:
                        # All methods failed
                        raise Exception(f"All authentication methods failed: token=True ({e1}), explicit token ({e2}), no auth ({e3})")
            
            # Ensure pad token is set
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token
            
            # Get the actual max length
            original_max_length = getattr(tokenizer, 'model_max_length', None)
            
            # Check if max length is problematic
            has_length_issue = False
            if original_max_length is None:
                max_length = 16384
                has_length_issue = True
                issue_reason = "No max_length specified"
            elif original_max_length > 100000:
                max_length = 16384
                has_length_issue = True
                issue_reason = f"Max length too large ({original_max_length})"
            elif original_max_length > 16384:
                max_length = original_max_length  # Keep original if reasonable
                has_length_issue = True
                issue_reason = f"Max length above 16384 ({original_max_length})"
            else:
                max_length = original_max_length
                issue_reason = "None"
            
            # Set the working max length
            tokenizer.model_max_length = min(max_length, 32768)  # Cap at 32k for safety
            
            tokenizers[name] = tokenizer
            tokenizer_info[name] = {
                'original_max_length': original_max_length,
                'working_max_length': tokenizer.model_max_length,
                'has_length_issue': has_length_issue,
                'issue_reason': issue_reason,
                'vocab_size': tokenizer.vocab_size,
                'loaded': True
            }
            
            status = "⚠️" if has_length_issue else "✅"
            print(f"  {status} Original max length: {original_max_length}")
            print(f"     Working max length: {tokenizer.model_max_length}")
            print(f"     Vocab size: {tokenizer.vocab_size}")
            if has_length_issue:
                print(f"     Issue: {issue_reason}")
                
        except Exception as e:
            print(f"❌ Failed to load {name}: {e}")
            tokenizer_info[name] = {
                'error': str(e),
                'loaded': False
            }
    
    # Print summary of tokenizer issues
    print("\n" + "="*60)
    print("TOKENIZER ANALYSIS:")
    print("="*60)
    
    problematic_tokenizers = []
    for name, info in tokenizer_info.items():
        if info.get('loaded', False) and info.get('has_length_issue', False):
            problematic_tokenizers.append(name)
            print(f"⚠️ {name}:")
            print(f"   Issue: {info['issue_reason']}")
            print(f"   Original: {info['original_max_length']} → Working: {info['working_max_length']}")
    
    loaded_count = sum(1 for info in tokenizer_info.values() if info.get('loaded', False))
    total_count = len(tokenizer_names)
    
    if not problematic_tokenizers:
        print("✅ All loaded tokenizers have reasonable max lengths (≤16384)")
    else:
        print(f"\n📊 Summary: {len(problematic_tokenizers)}/{loaded_count} loaded tokenizers have length issues")
    
    print(f"📊 Total loaded: {loaded_count}/{total_count} tokenizers")
    print("="*60 + "\n")
    
    return tokenizers, tokenizer_info

def download_sharegpt_data(use_no_sorry_version: bool = False) -> List[Dict[str, Any]]:
    """
    Download ShareGPT data from Hugging Face or load from local file if it exists.
    
    Args:
        use_no_sorry_version: If True, downloads version without "I'm sorry" responses
    
    Returns:
        List of ShareGPT conversations
    """
    # Determine filenames
    if use_no_sorry_version:
        local_filename = "sharegpt_data_no_sorry.json"
        url = "https://huggingface.co/datasets/anon8231489123/ShareGPT_Vicuna_unfiltered/resolve/main/ShareGPT_V3_unfiltered_cleaned_split_no_imsorry.json"
        print("Using ShareGPT data (no 'I'm sorry' version)...")
    else:
        local_filename = "sharegpt_data.json"
        url = "https://huggingface.co/datasets/anon8231489123/ShareGPT_Vicuna_unfiltered/resolve/main/ShareGPT_V3_unfiltered_cleaned_split.json"
        print("Using ShareGPT data (with 'I'm sorry' version)...")
    
    # Check if file already exists locally
    if os.path.exists(local_filename):
        try:
            print(f"✅ Found existing dataset: {local_filename}")
            print("Loading from local file...")
            with open(local_filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"Successfully loaded {len(data)} conversations from local file")
            return data
        except Exception as e:
            print(f"⚠️ Error reading local file {local_filename}: {e}")
            print("Will attempt to download fresh copy...")
    
    # Download if not exists or local file is corrupted
    try:
        print(f"📥 Downloading from Hugging Face...")
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        # Get file size for progress tracking
        total_size = int(response.headers.get('content-length', 0))
        
        print(f"💾 Saving to {local_filename}...")
        with open(local_filename, 'wb') as f:
            downloaded = 0
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"\rProgress: {progress:.1f}%", end="", flush=True)
        
        print(f"\n✅ Successfully downloaded to {local_filename}")
        
        # Load and verify the downloaded file
        with open(local_filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"📊 Dataset contains {len(data)} conversations")
        return data
        
    except Exception as e:
        print(f"❌ Error downloading ShareGPT data: {e}")
        
        # Try alternative local filenames as fallback
        fallback_files = [
            "sharegpt_data.json",
            "sharegpt_data_no_sorry.json", 
            "ShareGPT_V3_unfiltered_cleaned_split.json",
            "ShareGPT_V3_unfiltered_cleaned_split_no_imsorry.json"
        ]
        
        for fallback_file in fallback_files:
            if os.path.exists(fallback_file):
                try:
                    print(f"🔄 Trying fallback file: {fallback_file}")
                    with open(fallback_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    print(f"✅ Successfully loaded {len(data)} conversations from {fallback_file}")
                    return data
                except Exception as fallback_e:
                    print(f"❌ Failed to load {fallback_file}: {fallback_e}")
                    continue
        
        raise Exception(f"Could not download or find local ShareGPT data. Tried: {', '.join(fallback_files)}")


def extract_prompts_from_sharegpt(data: List[Dict[str, Any]], tokenizers: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
    """
    Extract user prompts from ShareGPT conversations and tokenize with all tokenizers.
    
    Args:
        data: ShareGPT conversation data
        tokenizers: Dictionary of tokenizers
    
    Returns:
        Dictionary mapping tokenizer name to list of prompt data
    """
    all_prompts = {name: [] for name in tokenizers.keys()}
    skipped_counts = {name: 0 for name in tokenizers.keys()}
    
    print("Extracting prompts from ShareGPT conversations...")
    
    for conv_idx, conversation in enumerate(data):
        if conv_idx % 1000 == 0:
            print(f"Processed {conv_idx}/{len(data)} conversations...")
        
        if "conversations" not in conversation:
            continue
        
        conv_turns = conversation["conversations"]
        if len(conv_turns) < 2:
            continue
        
        # Extract the first user message (prompt)
        for turn in conv_turns:
            if turn.get("from") == "human" and turn.get("value"):
                prompt = turn["value"].strip()
                if not prompt:
                    continue
                
                # Tokenize with each tokenizer
                for tokenizer_name, tokenizer in tokenizers.items():
                    try:
                        # Get tokenizer's max length, default to 16384 if not specified
                        max_length = getattr(tokenizer, 'model_max_length', 16384)
                        if max_length > 100000:  # Some tokenizers have very large defaults
                            max_length = 16384
                        
                        # First check prompt length to avoid very long sequences
                        if len(prompt) > max_length * 4:  # Rough estimate: 4 chars per token
                            skipped_counts[tokenizer_name] += 1
                            continue
                        
                        # Tokenize with truncation to avoid errors
                        tokens = tokenizer.encode(
                            prompt, 
                            add_special_tokens=True,
                            truncation=True,
                            max_length=max_length
                        )
                        token_count = len(tokens)
                        
                        # Skip very long prompts that would exceed our target ranges
                        if token_count > 20000:  # Skip extremely long prompts
                            skipped_counts[tokenizer_name] += 1
                            continue
                        
                        all_prompts[tokenizer_name].append({
                            'prompt': prompt,
                            'token_count': token_count,
                            'conversation_id': conv_idx
                        })
                    except Exception as e:
                        # Skip problematic prompts
                        skipped_counts[tokenizer_name] += 1
                        continue
                break  # Only take the first human turn as the prompt
    
    for name in tokenizers.keys():
        print(f"Extracted {len(all_prompts[name])} prompts for {name}")
        if skipped_counts[name] > 0:
            print(f"  Skipped {skipped_counts[name]} prompts (too long or problematic)")
    
    return all_prompts

def select_prompts_by_length(prompt_data: List[Dict[str, Any]], target_lengths: List[int]) -> Dict[int, Dict[str, Any]]:
    """
    Select exactly 1 prompt for each target token length.
    
    Args:
        prompt_data: List of dictionaries with 'prompt', 'token_count', and 'conversation_id'
        target_lengths: List of target token lengths to select
    
    Returns:
        Dictionary mapping length to a single selected prompt object
    """
    # Filter out extremely long prompts first
    filtered_data = [item for item in prompt_data if item.get('token_count', 0) <= 20000]
    print(f"Filtered data: {len(filtered_data)} prompts (removed {len(prompt_data) - len(filtered_data)} extremely long prompts)")
    
    # Group prompts by token count
    length_groups = {}
    for item in filtered_data:
        token_count = item.get('token_count', 0)
        if token_count not in length_groups:
            length_groups[token_count] = []
        length_groups[token_count].append(item)
    
    selected_prompts = {}
    
    for target_length in target_lengths:
        print(f"Selecting prompt for target length: {target_length}")
        
        # Start with exact matches, then expand tolerance
        tolerance = 0
        matching_prompts = []
        
        while len(matching_prompts) < 1 and tolerance <= target_length // 2:
            min_length = max(1, target_length - tolerance)
            max_length = target_length + tolerance
            
            matching_prompts = []
            for length, prompts in length_groups.items():
                if min_length <= length <= max_length:
                    matching_prompts.extend(prompts)
            
            if len(matching_prompts) >= 1:
                break
            
            # Increase tolerance
            tolerance = max(1, int(tolerance * 1.5)) if tolerance > 0 else max(1, target_length // 20)
        
        # If still not enough, find closest available lengths
        if len(matching_prompts) < 1:
            available_lengths = list(length_groups.keys())
            if available_lengths:
                # Sort by distance from target and pick the closest
                available_lengths.sort(key=lambda x: abs(x - target_length))
                # Take prompts from the closest length
                matching_prompts = length_groups[available_lengths[0]]
        
        # Select exactly 1 prompt
        if matching_prompts:
            # Pick the one with token count closest to target
            best_prompt = min(matching_prompts, key=lambda x: abs(x['token_count'] - target_length))
            selected_prompts[target_length] = best_prompt
            print(f"Selected 1 prompt for length {target_length}")
            print(f"  Actual token length: {best_prompt['token_count']} (diff: {abs(best_prompt['token_count'] - target_length)})")
        else:
            # No matching prompts found
            selected_prompts[target_length] = {
                'prompt': f"No prompt available for length {target_length}",
                'token_count': target_length,
                'conversation_id': -1
            }
            print(f"No suitable prompt found for length {target_length}")
    
    return selected_prompts

def main():
    """Main function to process ShareGPT data and select prompts by token length."""
    target_lengths = [128, 256, 512, 1024, 2048, 4096, 8192, 16384]
    
    # Set random seed for reproducibility
    random.seed(42)
    
    try:
        # Load tokenizers
        print("Loading tokenizers...")
        tokenizers, tokenizer_info = load_tokenizers()
        if not tokenizers:
            print("No tokenizers loaded successfully")
            return
        
        # Download ShareGPT data
        sharegpt_data = download_sharegpt_data(use_no_sorry_version=False)
        
        # Extract prompts and tokenize
        all_prompt_data = extract_prompts_from_sharegpt(sharegpt_data, tokenizers)
        
        # Select prompts by length for each tokenizer
        results = {}
        
        for tokenizer_name, prompt_data in all_prompt_data.items():
            print(f"\nProcessing {tokenizer_name}...")
            selected_prompts = select_prompts_by_length(prompt_data, target_lengths)
            
            # Format results for saving (keep only essential info)
            formatted_results = {}
            for length, prompt in selected_prompts.items():
                formatted_results[length] = {
                    'prompt': prompt['prompt'],
                    'token_count': prompt['token_count'],
                    'conversation_id': prompt['conversation_id']
                }
            
            results[tokenizer_name] = formatted_results
            
            # Print summary
            print(f"Summary for {tokenizer_name}:")
            for length in target_lengths:
                if length in formatted_results:
                    actual_count = formatted_results[length]['token_count']
                    diff = abs(actual_count - length)
                    print(f"  {length} tokens: Found {actual_count} tokens (diff: {diff})")
                else:
                    print(f"  {length} tokens: Not found")
        
        # Add tokenizer info to results
        results['_tokenizer_info'] = tokenizer_info
        
        # Save results
        output_filename = 'sharegpt_single_prompts_by_token_length.json'
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\nResults saved to {output_filename}")
        
        # Print overall summary
        print("\n" + "="*60)
        print("SUMMARY:")
        print("="*60)
        total_prompts = sum(len(data) for data in all_prompt_data.values())
        print(f"Total prompts processed: {total_prompts}")
        print(f"Target token lengths: {target_lengths}")
        print(f"Tokenizers used: {list(tokenizers.keys())}")
        print(f"Output file: {output_filename}")
        print(f"Selected: 1 prompt per target length per tokenizer")
        
        # Show tokenizers with length issues
        problematic = [name for name, info in tokenizer_info.items() 
                      if info.get('has_length_issue', False)]
        if problematic:
            print(f"\n⚠️ Tokenizers with max length issues:")
            for name in problematic:
                info = tokenizer_info[name]
                print(f"  - {name}: {info['issue_reason']}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()