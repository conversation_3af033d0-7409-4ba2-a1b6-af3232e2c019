# QPS Performance Testing Script

This script tests multiple language models with different QPS (Queries Per Second) rates using ShareGPT data, recording detailed performance metrics.

## Features

- **6 Models Tested**: DeepSeek, Phi-4, Qwen3, and Llama models
- **5 QPS Rates**: 2, 4, 8, 16, 32 requests per second
- **100 ShareGPT Prompts**: Uses sampled prompts with original length distribution
- **Detailed Metrics**: TTFT, throughput, response times, success rates
- **CSV Output**: Both detailed per-request and summary results

## Files

- `qps_performance_test.py` - Main testing script
- `test_config.json` - Configuration file (optional)
- `sampled_100_prompts_with_distribution.json` - ShareGPT sample data (required)

## Setup

1. **Install Dependencies**:
   ```bash
   pip install aiohttp asyncio
   ```

2. **Configure Model Ports**:
   Edit the script to set the correct ports for your models:
   - Each model runs on a different port (8001-8006 by default)
   - No API key required - uses direct port connections

3. **Ensure Data File Exists**:
   Make sure `sampled_100_prompts_with_distribution.json` is in the same directory

## Usage

```bash
python qps_performance_test.py
```

## How It Works

### QPS Implementation
- **Batch Processing**: For QPS=2, sends 2 requests, waits for completion, then next batch
- **Rate Control**: Maintains 1-second intervals between batches
- **Concurrent Requests**: Uses asyncio for parallel request handling within batches

### Metrics Collected

**Per Request**:
- `request_id`: Unique identifier
- `model`: Model name/key
- `port`: Port number for the model
- `qps`: QPS rate being tested
- `prompt_length`: Length of input prompt
- `start_time`: Request start timestamp
- `end_time`: Request completion timestamp
- `success`: Boolean success status
- `response_time`: Total response time (seconds)
- `ttft`: Time to first token (seconds)
- `total_tokens`: Number of tokens generated
- `throughput`: Tokens per second
- `error_message`: Error details if failed

**Summary Statistics**:
- Success rate percentage
- Average/median/P95/P99 response times
- Average/median/P95/P99 TTFT
- Average/median throughput
- Total test duration

## Output Files

### Detailed CSV Files
Format: `performance_detailed_{model}_{qps}qps_{timestamp}.csv`

Example: `performance_detailed_deepseek-ai_DeepSeek-R1-Distill-Llama-8B_2qps_20250110_143022.csv`

### Summary CSV File
Format: `performance_summary_{timestamp}.csv`

Contains aggregated statistics for all model/QPS combinations.

## Test Sequence

1. **Load Data**: Reads 100 ShareGPT prompts
2. **For Each Model**:
   - Test QPS 2, 4, 8, 16, 32 sequentially
   - 2-second pause between QPS tests
   - 5-second pause between models
3. **Save Results**: Individual detailed CSVs + summary CSV

## Expected Runtime

- **Per QPS Test**: ~50-100 seconds (100 prompts ÷ QPS rate)
- **Per Model**: ~8-15 minutes (5 QPS tests + pauses)
- **Total**: ~1-2 hours (6 models × 5 QPS rates)

## Customization

### Modify Models
Edit the `MODELS` list in the script:
```python
MODELS = [
    "your-model-1",
    "your-model-2",
    # ...
]
```

### Modify QPS Rates
Edit the `QPS_RATES` list:
```python
QPS_RATES = [1, 2, 4, 8]  # Custom rates
```

### Model Port Configuration
Update the MODELS dictionary:
```python
MODELS = {
    "your-model-1": {"port": 8001, "model_name": "your-model-1"},
    "your-model-2": {"port": 8002, "model_name": "your-model-2"},
    # ...
}
```

## Error Handling

- **Network Errors**: Logged and recorded as failed requests
- **API Errors**: HTTP status codes and messages captured
- **Timeout Errors**: 300-second timeout per request
- **Model Failures**: Test continues with remaining models

## Data Analysis

The CSV files can be analyzed to:
- Compare model performance across QPS rates
- Identify optimal QPS for each model
- Analyze scaling behavior and bottlenecks
- Generate performance charts and reports

## Troubleshooting

1. **No prompts loaded**: Ensure `sampled_100_prompts_with_distribution.json` exists
2. **API connection errors**: Check `API_BASE_URL` and network connectivity
3. **Authentication errors**: Verify `API_KEY` is correct
4. **Timeout errors**: Increase timeout in `aiohttp.ClientTimeout()`
5. **Memory issues**: Reduce batch sizes or add delays

## Example Results Structure

```csv
request_id,model,qps,prompt_length,start_time,end_time,success,response_time,ttft,total_tokens,throughput,error_message
0,deepseek-ai/DeepSeek-R1-Distill-Llama-8B,2,1234,1641825600.123,1641825602.456,True,2.333,0.245,150,64.3,
1,deepseek-ai/DeepSeek-R1-Distill-Llama-8B,2,567,1641825600.234,1641825601.789,True,1.555,0.189,89,57.2,
```

This comprehensive testing framework provides detailed insights into model performance characteristics across different load conditions.
