# Integration Changes Summary

## ✅ Changes Made to QPS Performance Test Script

### 1. **Added QPS-Level UUID Prefix**
- **Location**: `qps_performance_test.py`
- **Change**: Each QPS test gets a unique UUID that prefixes all prompts in that test
- **Implementation**:
  ```python
  # Generate QPS-level UUID
  qps_uuid = str(uuid.uuid4())
  
  # Prefix prompt with QPS UUID
  prefixed_prompt = f"[QPS_UUID:{qps_uuid}] {prompt}"
  ```

### 2. **Enhanced CSV Structure**
- **Added Fields**:
  - `qps_uuid`: QPS-level UUID for grouping requests
  - `prefixed_prompt_length`: Length of prompt with UUID prefix
- **CSV Structure**:
  ```csv
  request_id,request_uuid,qps_uuid,model,port,qps,prompt_length,prefixed_prompt_length,start_time,end_time,success,response_time,ttft,total_tokens,throughput,error_message
  ```

### 3. **Command Line Parameters**
- **Usage Examples**:
  ```bash
  # Test specific models
  python qps_performance_test.py --models "Qwen/Qwen3-8B" --qps 2 4 8 --prewarm
  
  # Quick test
  python qps_performance_test.py --models "Qwen/Qwen3-0.6B" --qps 2 --prompts-limit 10 --prewarm
  ```

## ✅ Changes Made to enhanced_model_iterator.sh

### 1. **Updated Script References**
- **Before**: `PYTHON_SCRIPT="simplified_model_tester.py"`
- **After**: `PYTHON_SCRIPT="qps_performance_test.py"`
- **Before**: `JSON_FILE="selected_texts_by_length.json"`
- **After**: `JSON_FILE="sampled_100_prompts_with_distribution.json"`

### 2. **Changed from Length Categories to QPS Rates**
- **Before**: `LENGTH_CATEGORIES=(64 128 256 512 1024 2048)`
- **After**: `QPS_RATES=(2 4 8 16 32)`

### 3. **Updated Model Mapping**
- **Mapped to QPS Script Models**:
  ```bash
  declare -A CONTAINER_TO_MODEL=(
      ["vllm-deepseek-r1-llama-8b"]="deepseek-ai/DeepSeek-R1-Distill-Llama-8B"
      ["vllm-phi-4-reasoning"]="microsoft/Phi-4-reasoning"
      ["vllm-deepseek-r1-qwen-14b"]="deepseek-ai/DeepSeek-R1-Distill-Qwen-14B"
      ["vllm-qwen3-8b"]="Qwen/Qwen3-8B"
      ["vllm-qwen3-0-6b"]="Qwen/Qwen3-0.6B"
      ["vllm-llama31-8b"]="meta-llama/Llama-3.1-8B-Instruct"
  )
  ```

### 4. **Updated Function Names and Logic**
- **Before**: `test_length_category()` → **After**: `test_qps_rate()`
- **Before**: `test_single_length_with_vm_boot()` → **After**: `test_single_qps_with_vm_boot()`
- **Updated**: `collect_vm_csv()` to work with QPS rates instead of length categories

### 5. **Updated Python Script Call**
- **Before**:
  ```bash
  HF_ENDPOINT="$HF_ENDPOINT" python3 "$PYTHON_SCRIPT" -m "$model_name" -l "$length_category" -j "$JSON_FILE" -c "$result_file" --no-prewarm
  ```
- **After**:
  ```bash
  HF_ENDPOINT="$HF_ENDPOINT" python3 "$PYTHON_SCRIPT" --models "$model_name" --qps "$qps_rate" --prewarm --prompts-limit 100
  ```

## 🔧 Key Integration Points

### 1. **VM to QPS Script Communication**
- **VM starts model service** on designated port (8001-8006)
- **QPS script connects** to `http://localhost:{port}/v1/chat/completions`
- **No API key required** - direct port connection

### 2. **File Output Coordination**
- **QPS Script Generates**: `performance_detailed_{model}_{qps}qps_{timestamp}.csv`
- **Shell Script Collects**: Moves files to organized directory structure
- **VM Generates**: Service readiness CSV files

### 3. **Model Port Mapping**
```
Container Name              → Model Name                                    → Port
vllm-deepseek-r1-llama-8b  → deepseek-ai/DeepSeek-R1-Distill-Llama-8B    → 8001
vllm-phi-4-reasoning       → microsoft/Phi-4-reasoning                    → 8002
vllm-deepseek-r1-qwen-14b  → deepseek-ai/DeepSeek-R1-Distill-Qwen-14B    → 8003
vllm-qwen3-8b              → Qwen/Qwen3-8B                               → 8004
vllm-qwen3-0-6b            → Qwen/Qwen3-0.6B                             → 8005
vllm-llama31-8b            → meta-llama/Llama-3.1-8B-Instruct            → 8006
```

## 🚀 Usage Workflow

### 1. **Start VM with Model**
```bash
./enhanced_model_iterator.sh
```

### 2. **VM Boots and Starts Model Service**
- Container starts vLLM service on designated port
- Service becomes ready and generates readiness CSV

### 3. **QPS Testing Begins**
- Shell script calls QPS Python script with specific model and QPS rate
- Python script connects to model service and runs performance tests
- Each prompt gets prefixed with QPS UUID: `[QPS_UUID:abc123...] original_prompt`

### 4. **Results Collection**
- QPS script generates detailed CSV files
- Shell script collects and organizes all CSV files
- VM CSV files are also collected for service metrics

## 📊 Expected Output Structure

```
results/
├── deepseek-ai_DeepSeek-R1-Distill-Llama-8B/
│   ├── deepseek-ai_DeepSeek-R1-Distill-Llama-8B_qps_2_20250110_143022.csv
│   ├── deepseek-ai_DeepSeek-R1-Distill-Llama-8B_qps_4_20250110_143122.csv
│   └── ...
├── microsoft_Phi-4-reasoning/
│   ├── microsoft_Phi-4-reasoning_qps_2_20250110_143222.csv
│   └── ...
└── performance_summary_20250110_143022.csv
```

## ✅ Ready to Use

The integration is complete and ready for testing. The enhanced_model_iterator.sh script will now:
1. Boot VMs with the correct models
2. Call the QPS performance test script with proper parameters
3. Collect and organize all performance data
4. Generate comprehensive CSV reports with UUID tracking
