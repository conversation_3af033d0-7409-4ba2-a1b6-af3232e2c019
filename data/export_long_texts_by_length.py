#!/usr/bin/env python3
"""
Script to export selected texts by length for longer token counts: 4096, 8192, 16384.
This script samples texts from ShareGPT dataset and groups them by these specific token lengths.
"""

import json
import random
import numpy as np
from typing import Dict, List, Any
from transformers import AutoTokenizer
from collections import defaultdict

def load_sharegpt_data(filename='sharegpt_data.json'):
    """Load ShareGPT data from JSON file."""
    print(f"Loading ShareGPT data from {filename}...")
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"Successfully loaded {len(data)} conversations")
        return data
    except FileNotFoundError:
        print(f"Error: {filename} not found")
        return None
    except json.JSONDecodeError:
        print(f"Error: Invalid JSON in {filename}")
        return None

def extract_all_texts(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Extract all text content from ShareGPT conversations (both human and assistant)."""
    texts = []
    
    for conversation in data:
        if 'conversations' not in conversation:
            continue
            
        for turn in conversation['conversations']:
            if 'value' in turn and turn['value'].strip():
                text_content = turn['value'].strip()
                texts.append({
                    'conversation_id': conversation.get('id', 'unknown'),
                    'text': text_content,
                    'role': turn.get('from', 'unknown')
                })
    
    print(f"Extracted {len(texts)} text segments from conversations")
    return texts

def tokenize_texts(texts: List[Dict[str, Any]], tokenizer_name: str = "Qwen/Qwen2.5-3B") -> List[Dict[str, Any]]:
    """Tokenize texts and add token counts."""
    print(f"Loading tokenizer: {tokenizer_name}")
    try:
        tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
    except Exception as e:
        print(f"Failed to load tokenizer {tokenizer_name}: {e}")
        print("Using a simple word-based approximation instead...")
        tokenizer = None
    
    print("Tokenizing texts...")
    tokenized_texts = []
    
    for i, text_item in enumerate(texts):
        if i % 10000 == 0:
            print(f"Processed {i}/{len(texts)} texts...")
        
        text = text_item['text']
        
        if tokenizer:
            try:
                tokens = tokenizer.encode(text, add_special_tokens=True)
                token_count = len(tokens)
            except Exception:
                # Fallback to word-based approximation
                token_count = int(len(text.split()) * 1.3)
        else:
            # Simple word-based approximation (words * 1.3 ≈ tokens)
            token_count = int(len(text.split()) * 1.3)
        
        tokenized_texts.append({
            'conversation_id': text_item['conversation_id'],
            'text': text,
            'role': text_item['role'],
            'token_count': token_count
        })
    
    return tokenized_texts

def select_texts_by_target_lengths(texts: List[Dict[str, Any]], 
                                 target_lengths: List[int] = [4096, 8192, 16384],
                                 texts_per_length: int = 10) -> Dict[str, List[str]]:
    """Select texts that match target token lengths."""
    
    # Group texts by token count ranges
    length_groups = defaultdict(list)
    
    for text_item in texts:
        token_count = text_item['token_count']
        
        # Find the closest target length
        for target_length in target_lengths:
            # Define tolerance ranges for each target length
            tolerance = target_length * 0.1  # 10% tolerance
            min_tokens = target_length - tolerance
            max_tokens = target_length + tolerance
            
            if min_tokens <= token_count <= max_tokens:
                length_groups[target_length].append(text_item)
                break
    
    # Select texts for each target length
    selected_texts = {}
    
    print(f"\nSelecting texts for target lengths: {target_lengths}")
    print("="*60)
    
    for target_length in target_lengths:
        available_texts = length_groups[target_length]
        
        if available_texts:
            # Sort by how close they are to the target length
            available_texts.sort(key=lambda x: abs(x['token_count'] - target_length))
            
            # Select the best matches
            selected_count = min(texts_per_length, len(available_texts))
            selected = available_texts[:selected_count]
            
            # Extract just the text content
            selected_texts[str(target_length)] = [item['text'] for item in selected]
            
            print(f"Target {target_length} tokens:")
            print(f"  Available texts: {len(available_texts)}")
            print(f"  Selected texts: {selected_count}")
            if selected:
                actual_lengths = [item['token_count'] for item in selected]
                print(f"  Actual token range: {min(actual_lengths)} - {max(actual_lengths)}")
                print(f"  Average tokens: {sum(actual_lengths)/len(actual_lengths):.1f}")
            print()
        else:
            print(f"Target {target_length} tokens:")
            print(f"  No texts found in range")
            print()
            selected_texts[str(target_length)] = []
    
    return selected_texts

def save_selected_texts(selected_texts: Dict[str, List[str]], 
                       output_file: str = 'selected_texts_by_length.json'):
    """Save selected texts to JSON file."""
    
    # Add some statistics
    total_texts = sum(len(texts) for texts in selected_texts.values())
    
    print(f"Saving {total_texts} selected texts to {output_file}")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(selected_texts, f, indent=2, ensure_ascii=False)
    
    print(f"Results saved to {output_file}")
    
    # Print summary
    print(f"\nSUMMARY:")
    print("="*40)
    for length, texts in selected_texts.items():
        print(f"{length} tokens: {len(texts)} texts")
    print(f"Total: {total_texts} texts")

def main():
    """Main function to export selected texts by length."""
    # Set random seed for reproducibility
    random.seed(42)
    np.random.seed(42)
    
    # Target lengths for longer texts
    target_lengths = [4096, 8192, 16384]
    texts_per_length = 10  # Number of texts to select for each length
    
    print("EXPORTING SELECTED TEXTS BY LENGTH")
    print("="*50)
    print(f"Target lengths: {target_lengths}")
    print(f"Texts per length: {texts_per_length}")
    print()
    
    # Load ShareGPT data
    data = load_sharegpt_data()
    if not data:
        return
    
    # Extract all texts (both human and assistant messages)
    texts = extract_all_texts(data)
    if not texts:
        print("No texts extracted")
        return
    
    # Tokenize texts
    tokenized_texts = tokenize_texts(texts)
    
    # Filter texts to only include those that might match our target lengths
    print(f"\nFiltering texts for target lengths...")
    min_target = min(target_lengths)
    max_target = max(target_lengths)
    
    # Use broader range for initial filtering
    filtered_texts = [
        text for text in tokenized_texts 
        if min_target * 0.8 <= text['token_count'] <= max_target * 1.2
    ]
    
    print(f"Filtered to {len(filtered_texts)} texts in target range")
    
    # Select texts by target lengths
    selected_texts = select_texts_by_target_lengths(
        filtered_texts, 
        target_lengths, 
        texts_per_length
    )
    
    # Save results
    save_selected_texts(selected_texts)
    
    print("\n" + "="*50)
    print("EXPORT COMPLETE")
    print("="*50)

if __name__ == "__main__":
    main()
