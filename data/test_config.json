{"api_settings": {"base_url": "http://localhost", "timeout_seconds": 300}, "models": {"deepseek-ai/DeepSeek-R1-Distill-Llama-8B": {"port": 8001, "model_name": "deepseek-ai/DeepSeek-R1-Distill-Llama-8B"}, "microsoft/Phi-4-reasoning": {"port": 8002, "model_name": "microsoft/Phi-4-reasoning"}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B": {"port": 8003, "model_name": "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B"}, "Qwen/Qwen3-8B": {"port": 8004, "model_name": "Qwen/Qwen3-8B"}, "Qwen/Qwen3-0.6B": {"port": 8005, "model_name": "Qwen/Qwen3-0.6B"}, "meta-llama/Llama-3.1-8B-Instruct": {"port": 8006, "model_name": "meta-llama/Llama-3.1-8B-Instruct"}}, "qps_rates": [2, 4, 8, 16, 32], "test_settings": {"max_tokens": 1000, "temperature": 0.7, "stream": true, "prompts_limit": 100, "pause_between_qps_tests": 2, "pause_between_models": 5}, "data_files": {"sharegpt_sample": "sampled_100_prompts_with_distribution.json"}}