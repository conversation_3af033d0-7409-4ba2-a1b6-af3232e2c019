#!/usr/bin/env python3
"""
Performance testing script for multiple models with ShareGPT data.
Tests 6 models with QPS rates from 2 to 32, recording detailed performance metrics.
"""

import json
import csv
import time
import asyncio
import aiohttp
import statistics
from datetime import datetime
from typing import List, Dict, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Model configurations
MODELS = [
    "deepseek-ai/DeepSeek-R1-Distill-Llama-8B",
    "microsoft/Phi-4-reasoning", 
    "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B",
    "Qwen/Qwen3-8B",
    "Qwen/Qwen3-0.6B",
    "meta-llama/Llama-3.1-8B-Instruct"
]

# QPS rates to test
QPS_RATES = [2, 4, 8, 16, 32]

# API configuration (adjust these based on your setup)
API_BASE_URL = "http://localhost:8000/v1/chat/completions"  # Adjust as needed
API_KEY = "your-api-key"  # Adjust as needed

class PerformanceTracker:
    """Track performance metrics for each request."""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        self.requests = []
        self.start_time = None
        self.end_time = None
    
    def add_request(self, request_data: Dict[str, Any]):
        """Add a completed request's performance data."""
        self.requests.append(request_data)
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """Calculate summary statistics for all requests."""
        if not self.requests:
            return {}
        
        response_times = [req['response_time'] for req in self.requests if req['success']]
        ttfts = [req['ttft'] for req in self.requests if req['success'] and req['ttft'] is not None]
        throughputs = [req['throughput'] for req in self.requests if req['success'] and req['throughput'] is not None]
        
        successful_requests = len([req for req in self.requests if req['success']])
        total_requests = len(self.requests)
        
        stats = {
            'total_requests': total_requests,
            'successful_requests': successful_requests,
            'failed_requests': total_requests - successful_requests,
            'success_rate': successful_requests / total_requests if total_requests > 0 else 0,
            'total_duration': self.end_time - self.start_time if self.start_time and self.end_time else 0,
        }
        
        if response_times:
            stats.update({
                'avg_response_time': statistics.mean(response_times),
                'median_response_time': statistics.median(response_times),
                'p95_response_time': statistics.quantiles(response_times, n=20)[18] if len(response_times) >= 20 else max(response_times),
                'p99_response_time': statistics.quantiles(response_times, n=100)[98] if len(response_times) >= 100 else max(response_times),
                'min_response_time': min(response_times),
                'max_response_time': max(response_times),
            })
        
        if ttfts:
            stats.update({
                'avg_ttft': statistics.mean(ttfts),
                'median_ttft': statistics.median(ttfts),
                'p95_ttft': statistics.quantiles(ttfts, n=20)[18] if len(ttfts) >= 20 else max(ttfts),
                'p99_ttft': statistics.quantiles(ttfts, n=100)[98] if len(ttfts) >= 100 else max(ttfts),
            })
        
        if throughputs:
            stats.update({
                'avg_throughput': statistics.mean(throughputs),
                'median_throughput': statistics.median(throughputs),
            })
        
        return stats

def load_sharegpt_sample_data(filename='sampled_100_prompts_with_distribution.json') -> List[str]:
    """Load the 100 ShareGPT sample prompts."""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Extract prompts from the sampled data
        prompts = []
        if 'sampled_prompts' in data:
            prompts = [item['text'] for item in data['sampled_prompts']]
        else:
            logger.error(f"No 'sampled_prompts' found in {filename}")
            return []
        
        logger.info(f"Loaded {len(prompts)} prompts from {filename}")
        return prompts
    
    except FileNotFoundError:
        logger.error(f"File {filename} not found")
        return []
    except json.JSONDecodeError:
        logger.error(f"Invalid JSON in {filename}")
        return []

async def make_api_request(session: aiohttp.ClientSession, model: str, prompt: str, request_id: int) -> Dict[str, Any]:
    """Make a single API request and track performance metrics."""
    request_start = time.time()
    ttft = None
    total_tokens = 0
    
    request_data = {
        'request_id': request_id,
        'model': model,
        'prompt_length': len(prompt),
        'start_time': request_start,
        'success': False,
        'response_time': 0,
        'ttft': None,
        'total_tokens': 0,
        'throughput': None,
        'error_message': None
    }
    
    try:
        payload = {
            "model": model,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "max_tokens": 1000,
            "temperature": 0.7,
            "stream": True  # Enable streaming to measure TTFT
        }
        
        headers = {
            "Authorization": f"Bearer {API_KEY}",
            "Content-Type": "application/json"
        }
        
        async with session.post(API_BASE_URL, json=payload, headers=headers) as response:
            if response.status == 200:
                first_token_received = False
                response_content = ""
                
                async for line in response.content:
                    if not first_token_received:
                        ttft = time.time() - request_start
                        first_token_received = True
                    
                    # Process streaming response (simplified)
                    line_text = line.decode('utf-8').strip()
                    if line_text.startswith('data: ') and not line_text.endswith('[DONE]'):
                        try:
                            chunk_data = json.loads(line_text[6:])
                            if 'choices' in chunk_data and chunk_data['choices']:
                                delta = chunk_data['choices'][0].get('delta', {})
                                if 'content' in delta:
                                    response_content += delta['content']
                                    total_tokens += 1
                        except json.JSONDecodeError:
                            continue
                
                request_end = time.time()
                response_time = request_end - request_start
                throughput = total_tokens / response_time if response_time > 0 else 0
                
                request_data.update({
                    'success': True,
                    'response_time': response_time,
                    'ttft': ttft,
                    'total_tokens': total_tokens,
                    'throughput': throughput,
                    'end_time': request_end
                })
                
            else:
                request_data.update({
                    'success': False,
                    'response_time': time.time() - request_start,
                    'error_message': f"HTTP {response.status}: {await response.text()}"
                })
    
    except Exception as e:
        request_data.update({
            'success': False,
            'response_time': time.time() - request_start,
            'error_message': str(e)
        })
    
    return request_data

async def test_model_with_qps(model: str, prompts: List[str], qps: int, tracker: PerformanceTracker) -> None:
    """Test a model with specific QPS rate."""
    logger.info(f"Testing {model} with QPS {qps}")
    
    tracker.reset()
    tracker.start_time = time.time()
    
    # Calculate batch size and delay
    batch_size = qps
    delay_between_batches = 1.0  # 1 second between batches
    
    async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=300)) as session:
        request_id = 0
        
        # Process prompts in batches
        for i in range(0, len(prompts), batch_size):
            batch_prompts = prompts[i:i + batch_size]
            batch_start = time.time()
            
            # Create tasks for this batch
            tasks = []
            for prompt in batch_prompts:
                task = make_api_request(session, model, prompt, request_id)
                tasks.append(task)
                request_id += 1
            
            # Wait for all requests in this batch to complete
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Record results
            for result in batch_results:
                if isinstance(result, dict):
                    tracker.add_request(result)
                else:
                    # Handle exceptions
                    tracker.add_request({
                        'request_id': request_id,
                        'model': model,
                        'success': False,
                        'response_time': 0,
                        'error_message': str(result)
                    })
            
            # Wait for next batch (maintain QPS rate)
            batch_duration = time.time() - batch_start
            if batch_duration < delay_between_batches:
                await asyncio.sleep(delay_between_batches - batch_duration)
    
    tracker.end_time = time.time()
    logger.info(f"Completed testing {model} with QPS {qps}")

def save_detailed_results_to_csv(model: str, qps: int, tracker: PerformanceTracker) -> str:
    """Save detailed per-request results to CSV."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"performance_detailed_{model.replace('/', '_')}_{qps}qps_{timestamp}.csv"
    
    fieldnames = [
        'request_id', 'model', 'qps', 'prompt_length', 'start_time', 'end_time',
        'success', 'response_time', 'ttft', 'total_tokens', 'throughput', 'error_message'
    ]
    
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for request in tracker.requests:
            row = request.copy()
            row['qps'] = qps
            row['end_time'] = row.get('end_time', row['start_time'] + row['response_time'])
            writer.writerow(row)
    
    logger.info(f"Detailed results saved to {filename}")
    return filename

def save_summary_results_to_csv(results: List[Dict[str, Any]]) -> str:
    """Save summary results for all tests to CSV."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"performance_summary_{timestamp}.csv"

    if not results:
        logger.warning("No results to save")
        return filename

    fieldnames = list(results[0].keys())

    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(results)

    logger.info(f"Summary results saved to {filename}")
    return filename

async def main():
    """Main function to run all performance tests."""
    logger.info("Starting performance testing for multiple models")

    # Load ShareGPT sample data
    prompts = load_sharegpt_sample_data()
    if not prompts:
        logger.error("No prompts loaded. Exiting.")
        return

    # Limit to 100 prompts as requested
    prompts = prompts[:100]
    logger.info(f"Using {len(prompts)} prompts for testing")

    summary_results = []
    tracker = PerformanceTracker()

    # Test each model with each QPS rate
    for model in MODELS:
        logger.info(f"Starting tests for model: {model}")

        for qps in QPS_RATES:
            try:
                # Test the model with current QPS
                await test_model_with_qps(model, prompts, qps, tracker)

                # Save detailed results
                detailed_filename = save_detailed_results_to_csv(model, qps, tracker)

                # Get summary statistics
                summary_stats = tracker.get_summary_stats()
                summary_stats.update({
                    'model': model,
                    'qps': qps,
                    'test_timestamp': datetime.now().isoformat(),
                    'detailed_csv_file': detailed_filename
                })

                summary_results.append(summary_stats)

                logger.info(f"Completed {model} @ {qps} QPS - Success rate: {summary_stats.get('success_rate', 0):.2%}")

                # Brief pause between different QPS tests for the same model
                await asyncio.sleep(2)

            except Exception as e:
                logger.error(f"Error testing {model} with QPS {qps}: {e}")
                summary_results.append({
                    'model': model,
                    'qps': qps,
                    'test_timestamp': datetime.now().isoformat(),
                    'error': str(e)
                })

        # Longer pause between different models
        logger.info(f"Completed all QPS tests for {model}")
        await asyncio.sleep(5)

    # Save summary results
    summary_filename = save_summary_results_to_csv(summary_results)

    logger.info("All performance tests completed!")
    logger.info(f"Summary results saved to: {summary_filename}")
    logger.info(f"Total tests run: {len(summary_results)}")

if __name__ == "__main__":
    asyncio.run(main())
