#!/usr/bin/env python3
"""
Performance testing script for multiple models with ShareGPT data.
Tests 6 models with QPS rates from 2 to 32, recording detailed performance metrics.
"""

import json
import csv
import time
import asyncio
import aiohttp
import statistics
import uuid
import argparse
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Model configurations with ports
MODELS = {
    "deepseek-ai/DeepSeek-R1-Distill-Llama-8B": {"port": 8001, "model_name": "deepseek-ai/DeepSeek-R1-Distill-Llama-8B"},
    "microsoft/Phi-4-reasoning": {"port": 8002, "model_name": "microsoft/Phi-4-reasoning"},
    "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B": {"port": 8003, "model_name": "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B"},
    "Qwen/Qwen3-8B": {"port": 8004, "model_name": "Qwen/Qwen3-8B"},
    "Qwen/Qwen3-0.6B": {"port": 8005, "model_name": "Qwen/Qwen3-0.6B"},
    "meta-llama/Llama-3.1-8B-Instruct": {"port": 8006, "model_name": "meta-llama/Llama-3.1-8B-Instruct"}
}

# QPS rates to test
QPS_RATES = [2, 4, 8, 16, 32]

# Base URL configuration
BASE_URL = "http://localhost"

class PerformanceTracker:
    """Track performance metrics for each request."""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        self.requests = []
        self.start_time = 0.0
        self.end_time = 0.0
    
    def add_request(self, request_data: Dict[str, Any]):
        """Add a completed request's performance data."""
        self.requests.append(request_data)
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """Calculate summary statistics for all requests."""
        if not self.requests:
            return {}
        
        response_times = [req['response_time'] for req in self.requests if req['success']]
        ttfts = [req['ttft'] for req in self.requests if req['success'] and req['ttft'] is not None]
        throughputs = [req['throughput'] for req in self.requests if req['success'] and req['throughput'] is not None]
        
        successful_requests = len([req for req in self.requests if req['success']])
        total_requests = len(self.requests)
        
        stats = {
            'total_requests': total_requests,
            'successful_requests': successful_requests,
            'failed_requests': total_requests - successful_requests,
            'success_rate': successful_requests / total_requests if total_requests > 0 else 0,
            'total_duration': self.end_time - self.start_time if self.start_time and self.end_time else 0,
        }
        
        if response_times:
            stats.update({
                'avg_response_time': statistics.mean(response_times),
                'median_response_time': statistics.median(response_times),
                'p95_response_time': statistics.quantiles(response_times, n=20)[18] if len(response_times) >= 20 else max(response_times),
                'p99_response_time': statistics.quantiles(response_times, n=100)[98] if len(response_times) >= 100 else max(response_times),
                'min_response_time': min(response_times),
                'max_response_time': max(response_times),
            })
        
        if ttfts:
            stats.update({
                'avg_ttft': statistics.mean(ttfts),
                'median_ttft': statistics.median(ttfts),
                'p95_ttft': statistics.quantiles(ttfts, n=20)[18] if len(ttfts) >= 20 else max(ttfts),
                'p99_ttft': statistics.quantiles(ttfts, n=100)[98] if len(ttfts) >= 100 else max(ttfts),
            })
        
        if throughputs:
            stats.update({
                'avg_throughput': statistics.mean(throughputs),
                'median_throughput': statistics.median(throughputs),
            })
        
        return stats

async def prewarm_model(model_key: str, prewarm_prompts: Optional[List[str]] = None) -> bool:
    """Prewarm a model by sending a few test requests."""
    logger.info(f"Prewarming model: {model_key}")

    if not prewarm_prompts:
        prewarm_prompts = [
            "Hello, how are you?",
            "What is the capital of France?",
            "Explain machine learning in simple terms."
        ]

    model_config = MODELS[model_key]
    port = model_config["port"]
    url = f"{BASE_URL}:{port}/v1/chat/completions"

    success_count = 0

    async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60)) as session:
        for i, prompt in enumerate(prewarm_prompts):
            try:
                payload = {
                    "model": model_config["model_name"],
                    "messages": [{"role": "user", "content": prompt}],
                    "max_tokens": 50,
                    "temperature": 0.7
                }

                headers = {"Content-Type": "application/json"}

                async with session.post(url, json=payload, headers=headers) as response:
                    if response.status == 200:
                        success_count += 1
                        logger.info(f"  Prewarm {i+1}/{len(prewarm_prompts)} successful")
                    else:
                        logger.warning(f"  Prewarm {i+1}/{len(prewarm_prompts)} failed: HTTP {response.status}")

                # Small delay between prewarm requests
                await asyncio.sleep(0.5)

            except Exception as e:
                logger.warning(f"  Prewarm {i+1}/{len(prewarm_prompts)} failed: {e}")

    success_rate = success_count / len(prewarm_prompts)
    logger.info(f"Prewarm completed for {model_key}: {success_count}/{len(prewarm_prompts)} successful ({success_rate:.1%})")

    return success_rate > 0.5  # Consider successful if more than 50% of prewarm requests succeed

def load_sharegpt_sample_data(filename='sampled_100_prompts_with_distribution.json') -> List[str]:
    """Load the 100 ShareGPT sample prompts."""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Extract prompts from the sampled data
        prompts = []
        if 'sampled_prompts' in data:
            prompts = [item['text'] for item in data['sampled_prompts']]
        else:
            logger.error(f"No 'sampled_prompts' found in {filename}")
            return []
        
        logger.info(f"Loaded {len(prompts)} prompts from {filename}")
        return prompts
    
    except FileNotFoundError:
        logger.error(f"File {filename} not found")
        return []
    except json.JSONDecodeError:
        logger.error(f"Invalid JSON in {filename}")
        return []

async def make_api_request(session: aiohttp.ClientSession, model_key: str, prompt: str, request_id: int) -> Dict[str, Any]:
    """Make a single API request and track performance metrics."""
    request_start = time.time()
    ttft = None
    total_tokens = 0
    request_uuid = str(uuid.uuid4())

    # Get model configuration
    model_config = MODELS[model_key]
    port = model_config["port"]
    model_name = model_config["model_name"]

    request_data = {
        'request_id': request_id,
        'request_uuid': request_uuid,
        'model': model_key,
        'port': port,
        'prompt_length': len(prompt),
        'start_time': request_start,
        'success': False,
        'response_time': 0,
        'ttft': None,
        'total_tokens': 0,
        'throughput': None,
        'error_message': None
    }

    try:
        # Construct URL for this model's port
        url = f"{BASE_URL}:{port}/v1/chat/completions"

        payload = {
            "model": model_name,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "max_tokens": 1000,
            "temperature": 0.7,
            "stream": True  # Enable streaming to measure TTFT
        }

        headers = {
            "Content-Type": "application/json"
        }

        async with session.post(url, json=payload, headers=headers) as response:
            if response.status == 200:
                first_token_received = False
                response_content = ""
                
                async for line in response.content:
                    if not first_token_received:
                        ttft = time.time() - request_start
                        first_token_received = True
                    
                    # Process streaming response (simplified)
                    line_text = line.decode('utf-8').strip()
                    if line_text.startswith('data: ') and not line_text.endswith('[DONE]'):
                        try:
                            chunk_data = json.loads(line_text[6:])
                            if 'choices' in chunk_data and chunk_data['choices']:
                                delta = chunk_data['choices'][0].get('delta', {})
                                if 'content' in delta:
                                    response_content += delta['content']
                                    total_tokens += 1
                        except json.JSONDecodeError:
                            continue
                
                request_end = time.time()
                response_time = request_end - request_start
                throughput = total_tokens / response_time if response_time > 0 else 0
                
                request_data.update({
                    'success': True,
                    'response_time': response_time,
                    'ttft': ttft,
                    'total_tokens': total_tokens,
                    'throughput': throughput,
                    'end_time': request_end
                })
                
            else:
                request_data.update({
                    'success': False,
                    'response_time': time.time() - request_start,
                    'error_message': f"HTTP {response.status}: {await response.text()}"
                })
    
    except Exception as e:
        request_data.update({
            'success': False,
            'response_time': time.time() - request_start,
            'error_message': str(e)
        })
    
    return request_data

async def test_model_with_qps(model_key: str, prompts: List[str], qps: int, tracker: PerformanceTracker) -> None:
    """Test a model with specific QPS rate."""
    logger.info(f"Testing {model_key} with QPS {qps}")

    tracker.reset()
    tracker.start_time = time.time()

    # Calculate batch size and delay
    batch_size = qps
    delay_between_batches = 1.0  # 1 second between batches

    async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=300)) as session:
        request_id = 0

        # Process prompts in batches
        for i in range(0, len(prompts), batch_size):
            batch_prompts = prompts[i:i + batch_size]
            batch_start = time.time()

            # Create tasks for this batch
            tasks = []
            for prompt in batch_prompts:
                task = make_api_request(session, model_key, prompt, request_id)
                tasks.append(task)
                request_id += 1

            # Wait for all requests in this batch to complete
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)

            # Record results
            for result in batch_results:
                if isinstance(result, dict):
                    tracker.add_request(result)
                else:
                    # Handle exceptions
                    tracker.add_request({
                        'request_id': request_id,
                        'model': model_key,
                        'success': False,
                        'response_time': 0,
                        'error_message': str(result)
                    })

            # Wait for next batch (maintain QPS rate)
            batch_duration = time.time() - batch_start
            if batch_duration < delay_between_batches:
                await asyncio.sleep(delay_between_batches - batch_duration)

    tracker.end_time = time.time()
    logger.info(f"Completed testing {model_key} with QPS {qps}")

def save_detailed_results_to_csv(model_key: str, qps: int, tracker: PerformanceTracker) -> str:
    """Save detailed per-request results to CSV."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"performance_detailed_{model_key.replace('/', '_')}_{qps}qps_{timestamp}.csv"

    fieldnames = [
        'request_id', 'request_uuid', 'model', 'port', 'qps', 'prompt_length', 'start_time', 'end_time',
        'success', 'response_time', 'ttft', 'total_tokens', 'throughput', 'error_message'
    ]
    
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for request in tracker.requests:
            row = request.copy()
            row['qps'] = qps
            row['end_time'] = row.get('end_time', row['start_time'] + row['response_time'])
            writer.writerow(row)
    
    logger.info(f"Detailed results saved to {filename}")
    return filename

def save_summary_results_to_csv(results: List[Dict[str, Any]]) -> str:
    """Save summary results for all tests to CSV."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"performance_summary_{timestamp}.csv"

    if not results:
        logger.warning("No results to save")
        return filename

    fieldnames = list(results[0].keys())

    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(results)

    logger.info(f"Summary results saved to {filename}")
    return filename

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='QPS Performance Testing for Multiple Models')

    parser.add_argument('--models', nargs='+',
                       choices=list(MODELS.keys()),
                       default=list(MODELS.keys()),
                       help='Models to test (default: all models)')

    parser.add_argument('--qps', nargs='+', type=int,
                       default=QPS_RATES,
                       help=f'QPS rates to test (default: {QPS_RATES})')

    parser.add_argument('--prewarm', action='store_true',
                       help='Prewarm models before testing')

    parser.add_argument('--prompts-limit', type=int, default=100,
                       help='Number of prompts to use for testing (default: 100)')

    parser.add_argument('--no-prewarm', action='store_true',
                       help='Skip prewarming (default: prewarm enabled)')

    return parser.parse_args()

async def main():
    """Main function to run all performance tests."""
    args = parse_arguments()

    logger.info("Starting performance testing for multiple models")
    logger.info(f"Models to test: {args.models}")
    logger.info(f"QPS rates: {args.qps}")
    logger.info(f"Prewarm enabled: {args.prewarm and not args.no_prewarm}")

    # Load ShareGPT sample data
    prompts = load_sharegpt_sample_data()
    if not prompts:
        logger.error("No prompts loaded. Exiting.")
        return

    # Limit prompts as requested
    prompts = prompts[:args.prompts_limit]
    logger.info(f"Using {len(prompts)} prompts for testing")

    summary_results = []
    tracker = PerformanceTracker()

    # Prewarm models if requested
    if args.prewarm and not args.no_prewarm:
        logger.info("Starting model prewarming...")
        for model_key in args.models:
            prewarm_success = await prewarm_model(model_key)
            if not prewarm_success:
                logger.warning(f"Prewarm failed for {model_key}, but continuing with tests...")
        logger.info("Prewarming completed")
        await asyncio.sleep(2)  # Brief pause after prewarming

    # Test each model with each QPS rate
    for model_key in args.models:
        logger.info(f"Starting tests for model: {model_key}")

        for qps in args.qps:
            try:
                # Test the model with current QPS
                await test_model_with_qps(model_key, prompts, qps, tracker)

                # Save detailed results
                detailed_filename = save_detailed_results_to_csv(model_key, qps, tracker)

                # Get summary statistics
                summary_stats = tracker.get_summary_stats()
                summary_stats.update({
                    'model': model_key,
                    'port': MODELS[model_key]['port'],
                    'qps': qps,
                    'test_timestamp': datetime.now().isoformat(),
                    'detailed_csv_file': detailed_filename
                })

                summary_results.append(summary_stats)

                logger.info(f"Completed {model_key} @ {qps} QPS - Success rate: {summary_stats.get('success_rate', 0):.2%}")

                # Brief pause between different QPS tests for the same model
                await asyncio.sleep(2)

            except Exception as e:
                logger.error(f"Error testing {model_key} with QPS {qps}: {e}")
                summary_results.append({
                    'model': model_key,
                    'port': MODELS[model_key]['port'],
                    'qps': qps,
                    'test_timestamp': datetime.now().isoformat(),
                    'error': str(e)
                })

        # Longer pause between different models
        logger.info(f"Completed all QPS tests for {model_key}")
        await asyncio.sleep(5)

    # Save summary results
    summary_filename = save_summary_results_to_csv(summary_results)

    logger.info("All performance tests completed!")
    logger.info(f"Summary results saved to: {summary_filename}")
    logger.info(f"Total tests run: {len(summary_results)}")

if __name__ == "__main__":
    asyncio.run(main())
