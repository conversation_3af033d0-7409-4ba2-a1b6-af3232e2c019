#!/usr/bin/env python3
"""
Simple test script to verify the QPS performance test setup.
"""

import json
import asyncio
import aiohttp
from qps_performance_test import MODELS, load_sharegpt_sample_data, make_api_request

async def test_single_request():
    """Test a single request to verify the setup."""
    print("Testing QPS Performance Script Setup")
    print("="*50)
    
    # Load sample data
    prompts = load_sharegpt_sample_data()
    if not prompts:
        print("❌ No prompts loaded")
        return
    
    print(f"✅ Loaded {len(prompts)} prompts")
    
    # Test configuration
    print(f"\nModel Configuration:")
    for model_key, config in MODELS.items():
        print(f"  {model_key}")
        print(f"    Port: {config['port']}")
        print(f"    Model Name: {config['model_name']}")
        print()
    
    # Test a single request (optional - only if you have a model running)
    print("To test actual API calls, make sure you have models running on the specified ports.")
    print("Example: Model on port 8001 should be accessible at http://localhost:8001/v1/chat/completions")
    
    # Show sample prompt
    if prompts:
        sample_prompt = prompts[0][:200] + "..." if len(prompts[0]) > 200 else prompts[0]
        print(f"\nSample prompt: {sample_prompt}")

if __name__ == "__main__":
    asyncio.run(test_single_request())
