#!/usr/bin/env python3
"""
Test script to verify QPS argument parsing and UUID generation.
"""

import sys
import uuid
from qps_performance_test import parse_arguments, MODELS

def test_argument_parsing():
    """Test the argument parsing functionality."""
    print("Testing QPS Performance Script Arguments")
    print("="*50)
    
    # Test default arguments
    print("1. Testing default arguments:")
    sys.argv = ['qps_performance_test.py']
    args = parse_arguments()
    print(f"   Models: {len(args.models)} models")
    print(f"   QPS rates: {args.qps}")
    print(f"   Prewarm: {args.prewarm}")
    print(f"   Prompts limit: {args.prompts_limit}")
    print()
    
    # Test specific models
    print("2. Testing specific models:")
    sys.argv = ['qps_performance_test.py', '--models', 'Qwen/Qwen3-8B', 'Qwen/Qwen3-0.6B']
    args = parse_arguments()
    print(f"   Selected models: {args.models}")
    print()
    
    # Test specific QPS
    print("3. Testing specific QPS rates:")
    sys.argv = ['qps_performance_test.py', '--qps', '2', '4', '8']
    args = parse_arguments()
    print(f"   Selected QPS: {args.qps}")
    print()
    
    # Test with prewarm
    print("4. Testing with prewarm:")
    sys.argv = ['qps_performance_test.py', '--prewarm']
    args = parse_arguments()
    print(f"   Prewarm enabled: {args.prewarm}")
    print()
    
    # Test UUID generation
    print("5. Testing UUID generation:")
    for i in range(3):
        test_uuid = str(uuid.uuid4())
        print(f"   UUID {i+1}: {test_uuid}")
    print()
    
    # Show model configuration
    print("6. Model configuration:")
    for model_key, config in MODELS.items():
        print(f"   {model_key}")
        print(f"     Port: {config['port']}")
        print(f"     URL: http://localhost:{config['port']}/v1/chat/completions")
    print()
    
    print("✅ All argument parsing tests passed!")

if __name__ == "__main__":
    test_argument_parsing()
