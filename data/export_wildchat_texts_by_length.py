#!/usr/bin/env python3
"""
Script to export selected texts by length from WildChat dataset for longer token counts: 4096, 8192, 16384.
Uses WildChat parquet files and base tokenizer for accurate token counting.
"""

import json
import random
import pandas as pd
import numpy as np
from typing import Dict, List, Any
from transformers import AutoTokenizer
from collections import defaultdict
import glob
import os
import tiktoken

def load_wildchat_data():
    """Load WildChat data from parquet files."""
    print("Loading WildChat data from parquet files...")
    
    # Find all parquet files
    parquet_files = glob.glob("train-*.parquet")
    if not parquet_files:
        print("No parquet files found. Looking for train-*.parquet files...")
        return None
    
    print(f"Found {len(parquet_files)} parquet files: {parquet_files}")
    
    # Load all parquet files
    dataframes = []
    for file in sorted(parquet_files):
        print(f"Loading {file}...")
        try:
            df = pd.read_parquet(file)
            dataframes.append(df)
            print(f"  Loaded {len(df)} rows from {file}")
        except Exception as e:
            print(f"  Error loading {file}: {e}")
    
    if not dataframes:
        print("No data loaded from parquet files")
        return None
    
    # Combine all dataframes
    combined_df = pd.concat(dataframes, ignore_index=True)
    print(f"Total combined data: {len(combined_df)} rows")
    
    return combined_df

def extract_texts_from_wildchat(df: pd.DataFrame) -> List[Dict[str, Any]]:
    """Extract only user queries/prompts from WildChat conversations."""
    texts = []

    print("Extracting texts from WildChat conversations...")

    for idx, row in df.iterrows():
        if idx % 10000 == 0:
            print(f"Processed {idx}/{len(df)} conversations...")

        # Extract conversation data
        conversation_id = row['conversation_id'] if 'conversation_id' in row else f'conv_{idx}'

        # WildChat has a 'conversation' field with numpy array of message dictionaries
        if 'conversation' in row:
            conversation = row['conversation']

            # conversation is a numpy array of dictionaries
            if hasattr(conversation, '__iter__'):
                try:
                    for turn_idx, turn in enumerate(conversation):
                        if isinstance(turn, dict) and 'content' in turn:
                            # Only extract user queries/prompts (role = 'user')
                            if turn.get('role') == 'user':
                                content = turn['content']
                                if content and str(content).strip():
                                    texts.append({
                                        'conversation_id': conversation_id,
                                        'turn_id': turn_idx,
                                        'text': str(content).strip(),
                                        'role': 'user'
                                    })
                except Exception as e:
                    # Skip problematic conversations
                    continue

    print(f"Extracted {len(texts)} user queries")
    return texts

def tokenize_texts_with_base_tokenizer(texts: List[Dict[str, Any]],
                                     tokenizer_name: str = "cl100k_base") -> List[Dict[str, Any]]:
    """Tokenize texts using cl100k_base tokenizer (GPT-4 tokenizer)."""
    print(f"Loading base tokenizer: {tokenizer_name}")

    try:
        if tokenizer_name == "cl100k_base":
            tokenizer = tiktoken.get_encoding("cl100k_base")
            print(f"Successfully loaded {tokenizer_name} tokenizer")
        else:
            # Fallback to transformers tokenizer
            tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token
            print(f"Successfully loaded {tokenizer_name} tokenizer")
    except Exception as e:
        print(f"Failed to load tokenizer {tokenizer_name}: {e}")
        print("Falling back to word-based approximation...")
        tokenizer = None
    
    print("Tokenizing texts...")
    tokenized_texts = []

    for i, text_item in enumerate(texts):
        if i % 5000 == 0:
            print(f"Tokenized {i}/{len(texts)} texts...")

        text = text_item['text']

        if tokenizer:
            try:
                # Check if it's tiktoken or transformers tokenizer
                if tokenizer_name == "cl100k_base":
                    # tiktoken tokenizer
                    tokens = tokenizer.encode(text)
                    token_count = len(tokens)
                else:
                    # transformers tokenizer
                    tokens = tokenizer.encode(text, add_special_tokens=True, truncation=False)
                    token_count = len(tokens)
            except Exception:
                # Fallback for very long texts or encoding issues
                try:
                    if tokenizer_name == "cl100k_base":
                        # tiktoken doesn't have special tokens parameter
                        tokens = tokenizer.encode(text)
                        token_count = len(tokens)
                    else:
                        # Try without special tokens for transformers
                        tokens = tokenizer.encode(text, add_special_tokens=False, truncation=False)
                        token_count = len(tokens)
                except:
                    # Final fallback to word approximation
                    token_count = int(len(text.split()) * 1.3)
        else:
            # Word-based approximation
            token_count = int(len(text.split()) * 1.3)
        
        tokenized_texts.append({
            'conversation_id': text_item['conversation_id'],
            'turn_id': text_item['turn_id'],
            'text': text,
            'role': text_item['role'],
            'token_count': token_count
        })
    
    print(f"Tokenization complete. Token count range: {min(t['token_count'] for t in tokenized_texts)} - {max(t['token_count'] for t in tokenized_texts)}")
    return tokenized_texts

def select_texts_by_target_lengths(texts: List[Dict[str, Any]], 
                                 target_lengths: List[int] = [4096, 8192, 16384],
                                 texts_per_length: int = 10) -> Dict[str, List[str]]:
    """Select texts that match target token lengths with tolerance."""
    
    print(f"\nSelecting texts for target lengths: {target_lengths}")
    print("="*60)
    
    selected_texts = {}
    
    for target_length in target_lengths:
        print(f"\nProcessing target length: {target_length} tokens")
        
        # Define tolerance (±10% of target length)
        tolerance = target_length * 0.1
        min_tokens = int(target_length - tolerance)
        max_tokens = int(target_length + tolerance)
        
        print(f"  Searching for texts with {min_tokens} - {max_tokens} tokens")
        
        # Find texts in the target range
        candidates = [
            text for text in texts 
            if min_tokens <= text['token_count'] <= max_tokens
        ]
        
        print(f"  Found {len(candidates)} candidate texts")
        
        if candidates:
            # Sort by how close they are to the target length
            candidates.sort(key=lambda x: abs(x['token_count'] - target_length))
            
            # Select the best matches
            selected_count = min(texts_per_length, len(candidates))
            selected = candidates[:selected_count]
            
            # Extract text content and add metadata
            selected_texts[str(target_length)] = []
            
            for item in selected:
                selected_texts[str(target_length)].append(item['text'])
            
            # Print statistics
            actual_lengths = [item['token_count'] for item in selected]
            print(f"  Selected {selected_count} texts")
            print(f"  Actual token range: {min(actual_lengths)} - {max(actual_lengths)}")
            print(f"  Average tokens: {sum(actual_lengths)/len(actual_lengths):.1f}")
            
            # Show a sample
            if selected:
                sample_text = selected[0]['text'][:200] + "..." if len(selected[0]['text']) > 200 else selected[0]['text']
                print(f"  Sample text ({selected[0]['token_count']} tokens): {sample_text}")
        else:
            print(f"  No texts found in range")
            selected_texts[str(target_length)] = []
    
    return selected_texts

def save_selected_texts(selected_texts: Dict[str, List[str]],
                       output_file: str = 'selected_texts_by_length.json'):
    """Save selected texts to JSON file, replacing any existing content."""

    total_texts = sum(len(texts) for texts in selected_texts.values())

    print(f"\nSaving {total_texts} selected user queries to {output_file}")
    print("⚠️  This will replace any existing content in the file.")

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(selected_texts, f, indent=2, ensure_ascii=False)

    print(f"✅ Results saved to {output_file}")

    # Print final summary
    print(f"\nFINAL SUMMARY:")
    print("="*40)
    for length, texts in selected_texts.items():
        print(f"{length} tokens: {len(texts)} user queries")
    print(f"Total: {total_texts} user queries")

def main():
    """Main function to export selected texts by length from WildChat."""
    # Set random seed for reproducibility
    random.seed(42)
    np.random.seed(42)
    
    # Configuration
    target_lengths = [4096, 8192, 16384]
    texts_per_length = 10
    base_tokenizer = "cl100k_base"  # Base tokenizer for consistent token counting
    
    print("EXPORTING WILDCHAT USER QUERIES BY LENGTH")
    print("="*50)
    print(f"Target lengths: {target_lengths}")
    print(f"User queries per length: {texts_per_length}")
    print(f"Base tokenizer: {base_tokenizer}")
    print("⚠️  Only extracting user queries/prompts (not assistant responses)")
    print("⚠️  This will replace existing selected_texts_by_length.json")
    print()
    
    # Load WildChat data
    df = load_wildchat_data()
    if df is None:
        print("Failed to load WildChat data")
        return
    
    # Extract texts from conversations
    texts = extract_texts_from_wildchat(df)
    if not texts:
        print("No texts extracted from WildChat data")
        return
    
    # Tokenize texts using base tokenizer
    tokenized_texts = tokenize_texts_with_base_tokenizer(texts, base_tokenizer)
    
    # Filter to focus on longer texts (save processing time)
    min_target = min(target_lengths)
    print(f"\nFiltering texts to focus on longer content (≥{min_target * 0.8} tokens)...")
    
    long_texts = [
        text for text in tokenized_texts 
        if text['token_count'] >= min_target * 0.8
    ]
    
    print(f"Filtered to {len(long_texts)} longer texts from {len(tokenized_texts)} total")
    
    # Select texts by target lengths
    selected_texts = select_texts_by_target_lengths(
        long_texts, 
        target_lengths, 
        texts_per_length
    )
    
    # Save results
    save_selected_texts(selected_texts)
    
    print("\n" + "="*50)
    print("EXPORT COMPLETE")
    print("="*50)

if __name__ == "__main__":
    main()
