import requests
import time
import json
import statistics
import csv
import os
from typing import Dict, <PERSON>, Tu<PERSON>, Optional
from dataclasses import dataclass
from datetime import datetime
from collections import defaultdict
import uuid

@dataclass
class PerformanceResult:
    """Performance result with core metrics"""
    model: str
    port: int
    ttft: float  # Time to first token
    tps_overall: float  # Overall tokens per second
    tps_after_first: float  # Tokens per second after first token
    total_time: float
    token_count: int
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int
    response: str
    finish_reason: str
    success: bool
    error: Optional[str] = None
    request_id: str = ""
    resource_test_id: str = ""

@dataclass
class ResourceMonitoringResult:
    """Resource monitoring results"""
    test_id: str
    samples: int
    duration: float
    avg_cpu_percent: float
    avg_memory_percent: float
    avg_memory_used_gb: float
    csv_file: str

class EnhancedModelPerformanceTester:
    def __init__(self, base_url: str = "http://localhost", resource_monitor_url: str = "http://localhost:8020"):
        self.base_url = base_url
        self.resource_monitor_url = resource_monitor_url
        self.models = {
            "Qwen3-14B": {"port": 8002, "model_name": "Qwen/Qwen3-14B"},
            "DeepSeek-R1-7B": {"port": 8004, "model_name": "deepseek-ai/deepseek-r1-distill-qwen-7b"},
            "Phi-4-14B": {"port": 8007, "model_name": "microsoft/phi-4"},
            "Phi-4-Mini": {"port": 8008, "model_name": "microsoft/Phi-4-mini-instruct"},
            "Ministral-8B": {"port": 8012, "model_name": "mistralai/Ministral-8B-Instruct-2410"},
            "Gemma3-27B": {"port": 8013, "model_name": "google/gemma-3-27b-it"},
            "Gemma3-4B": {"port": 8016, "model_name": "google/gemma-3-4b-it"},
            "Llama32-11B-Vision": {"port": 8017, "model_name": "meta-llama/Llama-3.2-11B-Vision-Instruct"},
            "Llama32-3B-AWQ": {"port": 8017, "model_name": "casperhansen/llama-3.2-3b-instruct-awq"},
            "Qwen3-4B": {"port": 8017, "model_name": "Qwen/Qwen3-4B"}

        }
        self.all_results = defaultdict(list)
    

    
    def start_resource_monitoring(self, model_key: str, interval: float = 0.1) -> str:
        """Start resource monitoring for a model"""
        try:
            response = requests.post(
                f"{self.resource_monitor_url}/start",
                json={"model_name": model_key, "interval": interval}
            )
            response.raise_for_status()
            result = response.json()
            print(f"Started resource monitoring for {model_key}, test_id: {result['test_id']}")
            return result["test_id"]
        except Exception as e:
            print(f"Failed to start resource monitoring: {e}")
            return str(uuid.uuid4())  # Return a dummy test_id
    
    def stop_resource_monitoring(self) -> Optional[ResourceMonitoringResult]:
        """Stop resource monitoring and get results"""
        try:
            response = requests.post(f"{self.resource_monitor_url}/stop")
            response.raise_for_status()
            result = response.json()
            
            monitoring_result = ResourceMonitoringResult(
                test_id=result["test_id"],
                samples=result["samples"],
                duration=result["duration"],
                avg_cpu_percent=result["avg_cpu_percent"],
                avg_memory_percent=result["avg_memory_percent"],
                avg_memory_used_gb=result["avg_memory_used_gb"],
                csv_file=result["csv_file"]
            )
            
            print(f"Resource monitoring results:")
            print(f"  Samples: {monitoring_result.samples}")
            print(f"  Duration: {monitoring_result.duration:.2f}s")
            print(f"  Avg CPU: {monitoring_result.avg_cpu_percent:.2f}%")
            print(f"  Avg Memory: {monitoring_result.avg_memory_percent:.2f}%")
            print(f"  Avg Memory Used: {monitoring_result.avg_memory_used_gb:.3f} GB")
            print(f"  CSV File: {monitoring_result.csv_file}")
            
            return monitoring_result
        except Exception as e:
            print(f"Failed to stop resource monitoring: {e}")
            return None
    
    def get_prompt_tokens_once(self, model_key: str, prompt: str, target_tokens: int = 100, temperature: float = 0.7) -> int:
        """Get prompt token count using non-streaming request (run only once)"""
        model_info = self.models[model_key]
        url = f"{self.base_url}:{model_info['port']}/v1/chat/completions"
        
        print(f"Getting prompt token count for {model_key} using non-streaming request...")
        
        # unique identifier for this test
        test_unique_id = str(uuid.uuid4())
        unique_prompt = f"Request ID: {test_unique_id}\n\n{prompt}"
        
        non_streaming_payload = {
            "model": model_info["model_name"],
            "messages": [
                {"role": "user", "content": unique_prompt}
            ],
            "max_tokens": 1, 
            "temperature": temperature,
            "stream": False
        }
        
        headers = {"Content-Type": "application/json"}
        
        try:
            response = requests.post(url, json=non_streaming_payload, headers=headers, timeout=300)
            response.raise_for_status()
            data = response.json()
            prompt_tokens = data.get("usage", {}).get("prompt_tokens", 0)
            print(f"Prompt token count: {prompt_tokens}")
            return prompt_tokens
        except Exception as e:
            print(f"Error getting prompt token count: {e}")
            return 0

    def test_model_performance(self, model_key: str, prompt: str, target_tokens: int = 100, 
                             temperature: float = 0.7, num_runs: int = 3) -> Dict:
        """Test model performance with detailed token latency metrics"""
        
        if model_key not in self.models:
            raise ValueError(f"Model {model_key} not found in available models")
        
        model_info = self.models[model_key]
        url = f"{self.base_url}:{model_info['port']}/v1/chat/completions"
        
        prompt_tokens = self.get_prompt_tokens_once(model_key, prompt, target_tokens, temperature)
        
        results = []
        
        for run in range(num_runs):
            print(f"Testing {model_key} - Run {run + 1}/{num_runs}")
            
            # Create a unique identifier for each run
            run_unique_id = str(uuid.uuid4())
            run_prompt = f"Request ID: {run_unique_id}\n\n{prompt}"
            
            payload = {
                "model": model_info["model_name"],
                "messages": [
                    {"role": "user", "content": run_prompt}
                ],
                "max_tokens": target_tokens + 50,  
                "temperature": temperature,
                "stream": True 
            }
            
            try:
                resource_test_id = self.start_resource_monitoring(model_key)
                
                result = self._process_streaming_response(url, payload)
                
                monitoring_result = self.stop_resource_monitoring()
                
                # Create performance result
                perf_result = PerformanceResult(
                    model=model_key,
                    port=model_info["port"],
                    ttft=result["ttft"],
                    tps_overall=result["tps_overall"],
                    tps_after_first=result["tps_after_first"],
                    total_time=result["total_time"],
                    token_count=result["token_count"],
                    prompt_tokens=prompt_tokens,
                    completion_tokens=result.get("completion_tokens", result["token_count"]),
                    total_tokens=prompt_tokens + result.get("completion_tokens", result["token_count"]),
                    response=result["response"],
                    finish_reason=result.get("finish_reason", "unknown"),
                    success=True,
                    request_id=run_unique_id,
                    resource_test_id=resource_test_id
                )
                
                results.append(perf_result)
                
                self.all_results[model_key].append(perf_result)
                
                print(f"TTFT: {perf_result.ttft:.3f}s")
                print(f"Overall TPS: {perf_result.tps_overall:.2f}")
                print(f"Decoding TPS (after first): {perf_result.tps_after_first:.2f}")
                print(f"Prompt tokens: {perf_result.prompt_tokens}")
                print(f"Completion tokens: {perf_result.completion_tokens}")
                if monitoring_result:
                    print(f"Resource monitoring: {monitoring_result.samples} samples, {monitoring_result.duration:.2f}s")
                
            except Exception as e:
                print(f"Error: {e}")
                
                try:
                    self.stop_resource_monitoring()
                except:
                    pass
                
                error_result = PerformanceResult(
                    model=model_key,
                    port=model_info["port"],
                    ttft=0, tps_overall=0, tps_after_first=0, 
                    total_time=0, token_count=0,
                    prompt_tokens=prompt_tokens,
                    completion_tokens=0, total_tokens=prompt_tokens,
                    response="", finish_reason="error",
                    success=False,
                    error=str(e),
                    request_id=run_unique_id,
                    resource_test_id=""
                )
                results.append(error_result)
                self.all_results[model_key].append(error_result)
            
            time.sleep(2)  # Delay between runs
        
        # Export individual request data
        self.export_request_data(model_key, results)
        
        # Calculate and return averages
        return self._calculate_averages(results)
    
    def _process_streaming_response(self, url, payload):
        """Process streaming response with basic metrics"""
        headers = {"Content-Type": "application/json"}
        
        start_time = time.time()
        response = requests.post(url, json=payload, headers=headers, stream=True, timeout=120)
        response.raise_for_status()
        
        first_token_time = None
        full_response = ""
        token_count = 0
        finish_reason = None
        usage_data = {}
        
        for line in response.iter_lines(decode_unicode=True):
            if line.startswith("data: "):
                data_str = line[6:]
                if data_str.strip() == "[DONE]":
                    break
                
                try:
                    data = json.loads(data_str)
                    if "choices" in data and len(data["choices"]) > 0:
                        choice = data["choices"][0]
                        
                        if "delta" in choice and "content" in choice["delta"]:
                            content = choice["delta"]["content"]
                            current_time = time.time()
                            
                            if content:
                                if first_token_time is None:
                                    first_token_time = current_time
                                
                                full_response += content
                                token_count += 1
                        
                        if "finish_reason" in choice:
                            finish_reason = choice["finish_reason"] or finish_reason
                    
                    # Capture usage data if available
                    if "usage" in data:
                        usage_data = data["usage"]
                        
                except json.JSONDecodeError:
                    continue
        
        end_time = time.time()
        total_time = end_time - start_time
        ttft = first_token_time - start_time if first_token_time else 0
        
        # Calculate overall TPS
        tps_overall = token_count / total_time if total_time > 0 else 0
        
        # Calculate TPS after first token (decoding TPS)
        time_after_first = total_time - ttft
        tps_after_first = (token_count - 1) / time_after_first if time_after_first > 0 and token_count > 1 else 0
        
        return {
            "ttft": ttft,
            "tps_overall": tps_overall,
            "tps_after_first": tps_after_first,
            "total_time": total_time,
            "token_count": token_count,
            "response": full_response,
            "finish_reason": finish_reason,
            "prompt_tokens": usage_data.get("prompt_tokens", 0),
            "completion_tokens": usage_data.get("completion_tokens", token_count),
            "total_tokens": usage_data.get("total_tokens", 0)
        }
    
    def _calculate_averages(self, results: List[PerformanceResult]) -> Dict:
        """Calculate averages from performance results"""
        valid_results = [r for r in results if r.success]
        
        if not valid_results:
            return {
                "model": results[0].model if results else "unknown",
                "port": results[0].port if results else 0,
                "runs": results,
                "averages": {
                    "ttft": 0, 
                    "tps_overall": 0, 
                    "tps_after_first": 0,
                    "total_time": 0, 
                    "tokens": 0,
                    "prompt_tokens": 0, 
                    "completion_tokens": 0, 
                    "total_tokens": 0
                },
                "success_rate": 0
            }
        
        return {
            "model": valid_results[0].model,
            "port": valid_results[0].port,
            "runs": results,
            "averages": {
                "ttft": statistics.mean([r.ttft for r in valid_results]),
                "tps_overall": statistics.mean([r.tps_overall for r in valid_results]),
                "tps_after_first": statistics.mean([r.tps_after_first for r in valid_results]),
                "total_time": statistics.mean([r.total_time for r in valid_results]),
                "tokens": statistics.mean([r.token_count for r in valid_results]),
                "prompt_tokens": statistics.mean([r.prompt_tokens for r in valid_results]),
                "completion_tokens": statistics.mean([r.completion_tokens for r in valid_results]),
                "total_tokens": statistics.mean([r.total_tokens for r in valid_results])
            },
            "success_rate": len(valid_results) / len(results)
        }
    
    def print_enhanced_summary(self, results: Dict):
        """Print enhanced summary with latency metrics"""
        print(f"\n{'='*80}")
        print("PERFORMANCE SUMMARY")
        print(f"{'='*80}")
        
        if isinstance(results, dict) and "averages" in results:
            results = {results["model"]: results}
        
        # Sort by TPS
        sorted_models = sorted(results.items(), 
                             key=lambda x: x[1]["averages"]["tps_overall"], reverse=True)
        
        print(f"{'Model':<20} {'TTFT':<8} {'TPS':<8} {'TPS-Decode':<10} {'Tokens':<8} {'Success':<8}")
        print("-" * 80)
        
        for model_key, result in sorted_models:
            avg = result["averages"]
            success_rate = result["success_rate"] * 100
            
            print(f"{model_key:<20} "
                  f"{avg['ttft']:.3f}   "
                  f"{avg['tps_overall']:.2f}    "
                  f"{avg['tps_after_first']:.2f}        "
                  f"{avg['completion_tokens']:.0f}      "
                  f"{success_rate:.0f}%")

    def export_request_data(self, model_key: str, results: List[PerformanceResult], output_file: Optional[str] = None):
        """Export individual request data to CSV file"""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            model_name = model_key.replace(" ", "_")
            output_file = f"metrics/{model_name}_requests_{timestamp}.csv"
        
        # Create directory if it doesn't exist
        os.makedirs("metrics", exist_ok=True)
        
        with open(output_file, 'w', newline='') as csvfile:
            fieldnames = ['run', 'request_id', 'resource_test_id', 'ttft', 'tps_overall', 
                         'tps_after_first', 'total_time', 'token_count', 'prompt_tokens', 
                         'completion_tokens', 'success', 'timestamp']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for i, result in enumerate(results):
                writer.writerow({
                    'run': i + 1,
                    'request_id': result.request_id,
                    'resource_test_id': result.resource_test_id,
                    'ttft': result.ttft,
                    'tps_overall': result.tps_overall,
                    'tps_after_first': result.tps_after_first,
                    'total_time': result.total_time,
                    'token_count': result.token_count,
                    'prompt_tokens': result.prompt_tokens,
                    'completion_tokens': result.completion_tokens,
                    'success': 1 if result.success else 0,
                    'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
                })
        
        print(f"Request data exported to {output_file}")
        print(f"Total requests: {len(results)}")

    def test_all_models(self, prompt: str, target_tokens: int = 100, num_runs: int = 3) -> Dict:
        """Test all models"""
        print(f"TESTING: {len(self.models)} models")
        print(f"Prompt: {prompt[:80]}...")
        print(f"Target tokens: {target_tokens}")
        print(f"Runs per model: {num_runs}")
        
        all_results = {}
        
        for i, model_key in enumerate(self.models.keys(), 1):
            print(f"\n{'='*80}")
            print(f"[{i}/{len(self.models)}] Testing {model_key}")
            print(f"{'='*80}")
            
            result = self.test_model_performance(
                model_key, prompt, target_tokens=target_tokens, num_runs=num_runs
            )
            all_results[model_key] = result
        
        return all_results

if __name__ == "__main__":
    tester = EnhancedModelPerformanceTester()
    
    print("\nMODEL TEST WITH REMOTE RESOURCE MONITORING")
    result = tester.test_model_performance(
        "Qwen3-4B", 
        "Explain machine learning algorithms in detail, covering supervised, unsupervised, and reinforcement learning approaches.", 
        target_tokens=200,
        num_runs=100 
    )
    
    tester.print_enhanced_summary(result)