#!/bin/bash
# Example usage scripts for QPS performance testing

echo "QPS Performance Testing Examples"
echo "================================"

# Example 1: Test all models with all QPS rates and prewarm
echo "Example 1: Test all models with prewarming"
echo "python qps_performance_test.py --prewarm"
echo ""

# Example 2: Test specific models only
echo "Example 2: Test specific models only"
echo "python qps_performance_test.py --models 'deepseek-ai/DeepSeek-R1-Distill-Llama-8B' 'Qwen/Qwen3-8B' --prewarm"
echo ""

# Example 3: Test specific QPS rates
echo "Example 3: Test specific QPS rates"
echo "python qps_performance_test.py --qps 2 4 8 --prewarm"
echo ""

# Example 4: Test single model with single QPS
echo "Example 4: Test single model with single QPS"
echo "python qps_performance_test.py --models 'Qwen/Qwen3-8B' --qps 4 --prewarm"
echo ""

# Example 5: Test without prewarming
echo "Example 5: Test without prewarming"
echo "python qps_performance_test.py --no-prewarm"
echo ""

# Example 6: Test with limited prompts
echo "Example 6: Test with limited prompts (50 instead of 100)"
echo "python qps_performance_test.py --prompts-limit 50 --prewarm"
echo ""

# Example 7: Quick test - single model, single QPS, limited prompts
echo "Example 7: Quick test"
echo "python qps_performance_test.py --models 'Qwen/Qwen3-0.6B' --qps 2 --prompts-limit 10 --prewarm"
echo ""

echo "Available models:"
echo "- deepseek-ai/DeepSeek-R1-Distill-Llama-8B (port 8001)"
echo "- microsoft/Phi-4-reasoning (port 8002)"
echo "- deepseek-ai/DeepSeek-R1-Distill-Qwen-14B (port 8003)"
echo "- Qwen/Qwen3-8B (port 8004)"
echo "- Qwen/Qwen3-0.6B (port 8005)"
echo "- meta-llama/Llama-3.1-8B-Instruct (port 8006)"
echo ""

echo "Available QPS rates: 2, 4, 8, 16, 32"
echo ""

echo "To run a specific example, copy and paste the command above."
