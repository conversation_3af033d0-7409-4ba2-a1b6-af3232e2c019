#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to sample 100 prompts from ShareGPT dataset while maintaining the original length distribution.
This script analyzes the token length distribution of the original dataset and samples proportionally.
"""

import json
import random
import numpy as np
from typing import Dict, List, Any, Tuple
from transformers import AutoTokenizer
from collections import Counter

def load_sharegpt_data(filename='sharegpt_data.json'):
    """Load ShareGPT data from JSON file."""
    print(f"Loading ShareGPT data from {filename}...")
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"Successfully loaded {len(data)} conversations")
        return data
    except FileNotFoundError:
        print(f"Error: {filename} not found")
        return None
    except json.JSONDecodeError:
        print(f"Error: Invalid JSON in {filename}")
        return None

def extract_user_prompts(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Extract all user prompts from ShareGPT conversations."""
    prompts = []
    
    for conversation in data:
        if 'conversations' not in conversation:
            continue
            
        for turn in conversation['conversations']:
            if turn.get('from') == 'human' and 'value' in turn:
                prompt_text = turn['value'].strip()
                if prompt_text:  # Skip empty prompts
                    prompts.append({
                        'conversation_id': conversation.get('id', 'unknown'),
                        'text': prompt_text
                    })
    
    print(f"Extracted {len(prompts)} user prompts from conversations")
    return prompts

def tokenize_prompts(prompts: List[Dict[str, Any]], tokenizer_name: str = "Qwen/Qwen2.5-3B") -> List[Dict[str, Any]]:
    """Tokenize prompts and add token counts."""
    print(f"Loading tokenizer: {tokenizer_name}")
    try:
        tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
    except Exception as e:
        print(f"Failed to load tokenizer {tokenizer_name}: {e}")
        print("Using a simple word-based approximation instead...")
        tokenizer = None
    
    print("Tokenizing prompts...")
    tokenized_prompts = []
    
    for i, prompt in enumerate(prompts):
        if i % 10000 == 0:
            print(f"Processed {i}/{len(prompts)} prompts...")
        
        text = prompt['text']
        
        if tokenizer:
            try:
                tokens = tokenizer.encode(text, add_special_tokens=True)
                token_count = len(tokens)
            except Exception:
                # Fallback to word-based approximation
                token_count = len(text.split()) * 1.3  # Rough approximation
        else:
            # Simple word-based approximation (words * 1.3 ≈ tokens)
            token_count = int(len(text.split()) * 1.3)
        
        tokenized_prompts.append({
            'conversation_id': prompt['conversation_id'],
            'text': text,
            'token_count': token_count
        })
    
    return tokenized_prompts

def analyze_length_distribution(prompts: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze the token length distribution of prompts."""
    token_counts = [p['token_count'] for p in prompts]
    token_array = np.array(token_counts)
    
    # Calculate statistics
    stats = {
        'total_prompts': len(prompts),
        'min_tokens': int(np.min(token_array)),
        'max_tokens': int(np.max(token_array)),
        'mean_tokens': float(np.mean(token_array)),
        'median_tokens': float(np.median(token_array)),
        'std_tokens': float(np.std(token_array)),
        'percentiles': {
            '10': float(np.percentile(token_array, 10)),
            '25': float(np.percentile(token_array, 25)),
            '50': float(np.percentile(token_array, 50)),
            '75': float(np.percentile(token_array, 75)),
            '90': float(np.percentile(token_array, 90)),
            '95': float(np.percentile(token_array, 95)),
            '99': float(np.percentile(token_array, 99))
        }
    }
    
    # Create length bins for distribution
    bins = [0, 10, 25, 50, 100, 200, 500, 1000, 2000, 5000, float('inf')]
    bin_labels = ['1-10', '11-25', '26-50', '51-100', '101-200', '201-500', '501-1000', '1001-2000', '2001-5000', '5000+']
    
    # Count prompts in each bin
    bin_counts = []
    for i in range(len(bins) - 1):
        count = sum(1 for tc in token_counts if bins[i] < tc <= bins[i+1])
        bin_counts.append(count)
    
    # Calculate proportions
    total = len(token_counts)
    distribution = {}
    for label, count in zip(bin_labels, bin_counts):
        distribution[label] = {
            'count': count,
            'proportion': count / total if total > 0 else 0
        }
    
    return {
        'statistics': stats,
        'distribution': distribution,
        'bins': bins,
        'bin_labels': bin_labels
    }

def sample_prompts_by_distribution(prompts: List[Dict[str, Any]], 
                                 distribution_info: Dict[str, Any], 
                                 sample_size: int = 100) -> List[Dict[str, Any]]:
    """Sample prompts maintaining the original length distribution."""
    bins = distribution_info['bins']
    bin_labels = distribution_info['bin_labels']
    distribution = distribution_info['distribution']
    
    # Group prompts by bins
    binned_prompts = {label: [] for label in bin_labels}
    
    for prompt in prompts:
        token_count = prompt['token_count']
        for i, label in enumerate(bin_labels):
            if bins[i] < token_count <= bins[i+1]:
                binned_prompts[label].append(prompt)
                break
    
    # Calculate target counts for each bin
    sampled_prompts = []
    
    print(f"\nSampling {sample_size} prompts with original distribution:")
    for label in bin_labels:
        available = len(binned_prompts[label])
        proportion = distribution[label]['proportion']
        target_count = max(1, round(sample_size * proportion)) if available > 0 else 0
        
        # Don't sample more than available
        actual_count = min(target_count, available)
        
        if actual_count > 0:
            selected = random.sample(binned_prompts[label], actual_count)
            sampled_prompts.extend(selected)
            print(f"  {label} tokens: {actual_count}/{available} prompts (target: {target_count}, {proportion:.1%})")
        else:
            print(f"  {label} tokens: 0/{available} prompts (no prompts available)")
    
    # If we have fewer than target, fill from largest bins
    while len(sampled_prompts) < sample_size:
        # Find bin with most available prompts not yet sampled
        best_bin = None
        best_available = 0
        
        for label in bin_labels:
            available = len([p for p in binned_prompts[label] if p not in sampled_prompts])
            if available > best_available:
                best_available = available
                best_bin = label
        
        if best_bin and best_available > 0:
            remaining = [p for p in binned_prompts[best_bin] if p not in sampled_prompts]
            sampled_prompts.append(random.choice(remaining))
        else:
            break
    
    # Shuffle the final sample
    random.shuffle(sampled_prompts)
    
    print(f"\nFinal sample: {len(sampled_prompts)} prompts")
    return sampled_prompts[:sample_size]

def save_results(sampled_prompts: List[Dict[str, Any]], 
                distribution_info: Dict[str, Any], 
                output_file: str = 'sampled_100_prompts_with_distribution.json'):
    """Save the sampled prompts and distribution info to JSON."""
    
    # Analyze the sample distribution
    sample_analysis = analyze_length_distribution(sampled_prompts)
    
    result = {
        'metadata': {
            'sample_size': len(sampled_prompts),
            'sampling_method': 'proportional_to_original_distribution',
            'random_seed': 42
        },
        'original_distribution': distribution_info,
        'sample_distribution': sample_analysis,
        'sampled_prompts': sampled_prompts
    }
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
    print(f"\nResults saved to {output_file}")
    return result

def main():
    """Main function to sample 100 prompts with original distribution."""
    # Set random seed for reproducibility
    random.seed(42)
    np.random.seed(42)
    
    # Load ShareGPT data
    data = load_sharegpt_data()
    if not data:
        return
    
    # Extract user prompts
    prompts = extract_user_prompts(data)
    if not prompts:
        print("No prompts extracted")
        return
    
    # Tokenize prompts
    tokenized_prompts = tokenize_prompts(prompts)
    
    # Analyze length distribution
    print("\nAnalyzing length distribution...")
    distribution_info = analyze_length_distribution(tokenized_prompts)
    
    # Print distribution summary
    stats = distribution_info['statistics']
    print(f"\nDataset Statistics:")
    print(f"  Total prompts: {stats['total_prompts']:,}")
    print(f"  Token range: {stats['min_tokens']} - {stats['max_tokens']}")
    print(f"  Mean tokens: {stats['mean_tokens']:.1f}")
    print(f"  Median tokens: {stats['median_tokens']:.1f}")
    
    print(f"\nLength Distribution:")
    for label, info in distribution_info['distribution'].items():
        print(f"  {label} tokens: {info['count']:,} prompts ({info['proportion']:.1%})")
    
    # Sample 100 prompts maintaining distribution
    sampled_prompts = sample_prompts_by_distribution(tokenized_prompts, distribution_info, 100)
    
    # Save results
    save_results(sampled_prompts, distribution_info)
    
    print("\n" + "="*60)
    print("SAMPLING COMPLETE")
    print("="*60)

if __name__ == "__main__":
    main()
